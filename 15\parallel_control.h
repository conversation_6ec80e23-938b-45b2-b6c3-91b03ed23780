/*
 * CORDON-82 v15.0a Arduino Edition
 * СИСТЕМА ПАРАЛЛЕЛЬНОГО УПРАВЛЕНИЯ
 * 
 * Неблокирующее управление моторами для параллельного выполнения операций
 */

#ifndef PARALLEL_CONTROL_H
#define PARALLEL_CONTROL_H

#include <Arduino.h>
#include "config.h"

// ===== СТРУКТУРЫ ДАННЫХ =====

// Состояние мотора
typedef struct {
    bool active;                    // Мотор активен
    bool completed;                 // Операция завершена
    int motor_number;               // Номер мотора (1-7)
    int direction;                  // Направление движения
    uint16_t steps_total;           // Общее количество шагов
    uint16_t steps_remaining;       // Оставшиеся шаги
    uint16_t pulse_time;            // Время импульса
    uint16_t pause_time;            // Время паузы
    bool use_microseconds;          // Использовать микросекунды
    uint32_t last_step_time;        // Время последнего шага
    uint8_t step_phase;             // Фаза шага (0=LOW, 1=HIGH)
} MotorState_t;

// Состояние параллельной операции
typedef struct {
    bool active;                    // Операция активна
    bool completed;                 // Операция завершена
    uint8_t motors_count;           // Количество моторов в операции
    uint8_t motors_completed;       // Количество завершенных моторов
    uint32_t start_time;            // Время начала операции
    uint32_t timeout_ms;            // Таймаут операции
    char operation_name[32];        // Название операции
} ParallelOperation_t;

// ===== ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ =====
extern MotorState_t motor_states[8];           // Состояния моторов M1-M7 (индекс 0 не используется)
extern ParallelOperation_t current_operation;  // Текущая параллельная операция

// ===== ИНИЦИАЛИЗАЦИЯ =====
void initializeParallelControl();

// ===== ОСНОВНЫЕ ФУНКЦИИ =====

// Запуск неблокирующего движения мотора
bool startMotorMovement(int motor, int direction, uint16_t steps, uint16_t pulse_time, uint16_t pause_time, bool use_microseconds = false);

// Остановка мотора
void stopMotorMovement(int motor);

// Проверка завершения движения мотора
bool isMotorCompleted(int motor);

// Проверка активности мотора
bool isMotorActive(int motor);

// Обновление состояния всех моторов (вызывать в loop())
void updateMotorStates();

// ===== ПАРАЛЛЕЛЬНЫЕ ОПЕРАЦИИ =====

// Запуск параллельной операции
bool startParallelOperation(const char* operation_name, uint32_t timeout_ms = 10000);

// Добавление мотора в параллельную операцию
bool addMotorToOperation(int motor, int direction, uint16_t steps, uint16_t pulse_time, uint16_t pause_time, bool use_microseconds = false);

// Проверка завершения параллельной операции
bool isParallelOperationCompleted();

// Ожидание завершения параллельной операции
bool waitForParallelOperation(uint32_t timeout_ms = 0);

// Остановка параллельной операции
void stopParallelOperation();

// ===== ВЫСОКОУРОВНЕВЫЕ ПАРАЛЛЕЛЬНЫЕ ФУНКЦИИ =====

// Параллельное наведение M1+M2
bool parallelTargeting(uint16_t azimuth_steps, uint16_t elevation_steps);

// Параллельное возвращение домой M1+M2
bool parallelReturnHome();

// Параллельный тест моторов M1-M6
bool parallelMotorTest();

// Параллельная подготовка снаряда M3+M6
bool parallelShellPreparation();

// ===== МАКРОСЫ ДЛЯ ПАРАЛЛЕЛЬНОСТИ =====

// Неблокирующие макросы движений
#define START_M1_CW_ASYNC(steps, speed) \
    startMotorMovement(1, DIR_CW, steps, M1_##speed##_PULSE, M1_##speed##_PAUSE, false)

#define START_M1_CCW_ASYNC(steps, speed) \
    startMotorMovement(1, DIR_CCW, steps, M1_##speed##_PULSE, M1_##speed##_PAUSE, false)

#define START_M2_UP_ASYNC(steps, speed) \
    startMotorMovement(2, DIR_CW, steps, M2_##speed##_PULSE, M2_##speed##_PAUSE, false)

#define START_M2_DOWN_ASYNC(steps, speed) \
    startMotorMovement(2, DIR_CCW, steps, M2_##speed##_PULSE, M2_##speed##_PAUSE, false)

#define START_M3_FWD_ASYNC(steps, speed) \
    startMotorMovement(3, DIR_FORWARD, steps, M3_##speed##_PULSE, M3_##speed##_PAUSE, false)

#define START_M3_BACK_ASYNC(steps, speed) \
    startMotorMovement(3, DIR_BACK, steps, M3_##speed##_PULSE, M3_##speed##_PAUSE, false)

#define START_M4_FWD_ASYNC(steps, speed) \
    startMotorMovement(4, DIR_FORWARD, steps, M4_##speed##_PULSE, M4_##speed##_PAUSE, true)

#define START_M4_BACK_ASYNC(steps, speed) \
    startMotorMovement(4, DIR_BACK, steps, M4_##speed##_PULSE, M4_##speed##_PAUSE, true)

#define START_M5_FWD_ASYNC(steps, speed) \
    startMotorMovement(5, DIR_FORWARD, steps, M5_##speed##_PULSE, M5_##speed##_PAUSE, false)

#define START_M5_BACK_ASYNC(steps, speed) \
    startMotorMovement(5, DIR_BACK, steps, M5_##speed##_PULSE, M5_##speed##_PAUSE, false)

#define START_M6_FWD_ASYNC(steps, speed) \
    startMotorMovement(6, DIR_FORWARD, steps, M6_##speed##_PULSE, M6_##speed##_PAUSE, false)

#define START_M6_BACK_ASYNC(steps, speed) \
    startMotorMovement(6, DIR_BACK, steps, M6_##speed##_PULSE, M6_##speed##_PAUSE, false)

// Проверка завершения
#define IS_M1_COMPLETED() isMotorCompleted(1)
#define IS_M2_COMPLETED() isMotorCompleted(2)
#define IS_M3_COMPLETED() isMotorCompleted(3)
#define IS_M4_COMPLETED() isMotorCompleted(4)
#define IS_M5_COMPLETED() isMotorCompleted(5)
#define IS_M6_COMPLETED() isMotorCompleted(6)

// Ожидание завершения
#define WAIT_FOR_M1() while(!isMotorCompleted(1)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_M2() while(!isMotorCompleted(2)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_M3() while(!isMotorCompleted(3)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_M4() while(!isMotorCompleted(4)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_M5() while(!isMotorCompleted(5)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_M6() while(!isMotorCompleted(6)) { updateMotorStates(); delay(1); }

// Ожидание завершения нескольких моторов
#define WAIT_FOR_M1_M2() while(!isMotorCompleted(1) || !isMotorCompleted(2)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_M3_M4() while(!isMotorCompleted(3) || !isMotorCompleted(4)) { updateMotorStates(); delay(1); }
#define WAIT_FOR_ALL_MOTORS() while(!isMotorCompleted(1) || !isMotorCompleted(2) || !isMotorCompleted(3) || !isMotorCompleted(4) || !isMotorCompleted(5) || !isMotorCompleted(6)) { updateMotorStates(); delay(1); }

// ===== ДИАГНОСТИКА =====
void printParallelStatus();
void printMotorStates();
uint32_t getOperationElapsedTime();
float getOperationProgress();

// ===== БЕЗОПАСНОСТЬ =====
void emergencyStopAllParallel();
bool checkParallelSafety();

#endif // PARALLEL_CONTROL_H
