/*
 * CORDON-82 v15.0a Arduino Edition
 * УРОВЕНЬ 3: ПОЛНЫЕ СЦЕНАРИИ
 * 
 * Комплексные операции, составленные из микросценариев и макросов
 * Полный сценарий = завершенная боевая операция (например, полный цикл стрельбы)
 */

#ifndef FULL_SCENARIOS_H
#define FULL_SCENARIOS_H

#include "micro_scenarios.h"
#include "motor_macros.h"
#include "sensor_manager.h"
#include "config.h"

// ===== ОСНОВНЫЕ БОЕВЫЕ СЦЕНАРИИ =====

// Полный цикл стрельбы - главный сценарий системы
void FULL_FIRE_CYCLE(uint16_t azimuth_degrees, uint16_t elevation_degrees);

// Быстрое наведение и стрельба
void RAPID_FIRE_SEQUENCE(uint16_t azimuth, uint16_t elevation);

// Серийная стрельба (несколько выстрелов подряд)
void BURST_FIRE_SEQUENCE(uint16_t azimuth, uint16_t elevation, uint8_t rounds);

// Стрельба с автоматическим поиском цели
void AUTO_TARGET_FIRE_SEQUENCE();

// ===== СЦЕНАРИИ ПОДГОТОВКИ =====

// Полная инициализация системы
void SYSTEM_STARTUP_SEQUENCE();

// Калибровка всех систем
void FULL_SYSTEM_CALIBRATION();

// Подготовка к боевой работе
void COMBAT_READY_SEQUENCE();

// Приведение в походное положение
void TRAVEL_POSITION_SEQUENCE();

// ===== СЦЕНАРИИ ЗАРЯЖАНИЯ =====

// Полный цикл заряжания одного снаряда
void COMPLETE_LOADING_SEQUENCE();

// Быстрое заряжание (экстренный режим)
void RAPID_LOADING_SEQUENCE();

// Заряжание с проверкой всех систем
void SAFE_LOADING_SEQUENCE();

// Разряжание системы
void UNLOADING_SEQUENCE();

// ===== СЦЕНАРИИ НАВЕДЕНИЯ =====

// Точное наведение на координаты
void PRECISION_TARGETING(uint16_t azimuth, uint16_t elevation);

// Быстрое наведение (боевой режим)
void RAPID_TARGETING(uint16_t azimuth, uint16_t elevation);

// Сканирование сектора
void SECTOR_SCAN(uint16_t start_azimuth, uint16_t end_azimuth, uint16_t elevation);

// Возврат в исходное положение
void RETURN_TO_HOME_POSITION();

// ===== СЦЕНАРИИ ОБСЛУЖИВАНИЯ =====

// Полная диагностика системы
void FULL_SYSTEM_DIAGNOSTICS();

// Тестирование всех механизмов
void COMPLETE_MECHANISM_TEST();

// Профилактическое обслуживание
void MAINTENANCE_SEQUENCE();

// Смазка всех механизмов
void LUBRICATION_SEQUENCE();

// ===== АВАРИЙНЫЕ СЦЕНАРИИ =====

// Аварийная остановка всех систем
void EMERGENCY_SHUTDOWN();

// Аварийное разряжание
void EMERGENCY_UNLOAD();

// Приведение в безопасное положение
void EMERGENCY_SAFE_POSITION();

// Восстановление после аварии
void EMERGENCY_RECOVERY();

// ===== ДЕМОНСТРАЦИОННЫЕ СЦЕНАРИИ =====

// Полная демонстрация возможностей
void FULL_DEMONSTRATION();

// Демонстрация скоростных режимов
void SPEED_DEMONSTRATION();

// Демонстрация точности наведения
void PRECISION_DEMONSTRATION();

// Демонстрация автоматизации
void AUTOMATION_DEMONSTRATION();

// ===== ТРЕНИРОВОЧНЫЕ СЦЕНАРИИ =====

// Тренировка расчета (без боеприпасов)
void TRAINING_SEQUENCE();

// Имитация боевой работы
void COMBAT_SIMULATION();

// Тренировка быстрого наведения
void RAPID_TARGETING_TRAINING();

// Тренировка серийной стрельбы
void BURST_FIRE_TRAINING();

// ===== СПЕЦИАЛЬНЫЕ СЦЕНАРИИ =====

// Работа в условиях помех
void INTERFERENCE_MODE_SEQUENCE();

// Работа при низком заряде батареи
void LOW_POWER_MODE_SEQUENCE();

// Работа в экстремальных условиях
void EXTREME_CONDITIONS_SEQUENCE();

// Скрытый режим работы (минимум шума)
void STEALTH_MODE_SEQUENCE();

// ===== СЦЕНАРИИ КОНТРОЛЯ КАЧЕСТВА =====

// Проверка точности механизмов
void ACCURACY_TEST_SEQUENCE();

// Проверка скорости операций
void SPEED_TEST_SEQUENCE();

// Проверка надежности
void RELIABILITY_TEST_SEQUENCE();

// Проверка безопасности
void SAFETY_TEST_SEQUENCE();

// ===== СЦЕНАРИИ НАСТРОЙКИ =====

// Настройка скоростных режимов
void SPEED_CALIBRATION_SEQUENCE();

// Настройка датчиков
void SENSOR_CALIBRATION_SEQUENCE();

// Настройка энкодеров
void ENCODER_CALIBRATION_SEQUENCE();

// Настройка пределов движения
void LIMITS_CALIBRATION_SEQUENCE();

// ===== УТИЛИТАРНЫЕ ФУНКЦИИ =====

// Проверка готовности к выполнению сценария
bool checkScenarioReadiness();

// Отчет о выполнении сценария
void reportScenarioStatus(const char* scenario_name, bool success);

// Логирование операций
void logScenarioStep(const char* step_description);

// Проверка безопасности перед операцией
bool checkSafetyConditions();

#endif // FULL_SCENARIOS_H
