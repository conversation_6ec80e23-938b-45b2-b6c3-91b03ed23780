#include "main.h"
#include "demo_scenario.h"

// Файл интеграции демо-сценария с основной прошивкой
// Добавляет возможность запуска демо без изменения основного кода

// Переменные для интеграции
static uint8_t demo_integration_enabled = 1;
static uint32_t demo_check_counter = 0;

// Функция для интеграции демо в основной цикл
// Вызывается из основного цикла main.c
void Demo_Integration_Process(void)
{
    if (!demo_integration_enabled) return;
    
    // Проверяем команды запуска демо каждые 100 итераций
    demo_check_counter++;
    if (demo_check_counter >= 100)
    {
        demo_check_counter = 0;
        
        // Проверяем команду запуска демо только если демо не активно
        if (!Demo_Is_Active())
        {
            Demo_Check_Start_Command();
        }
    }
    
    // Если демо активно, продолжаем выполнение
    if (Demo_Is_Active())
    {
        // Здесь можно добавить дополнительную логику
        // например, обработку прерываний демо
    }
}

// Функция для добавления команды демо в UART обработчик
// Добавляет новую команду CMD_100 для запуска демо
uint8_t Demo_Integration_Handle_UART_Command(uint8_t cmd_number)
{
    switch(cmd_number)
    {
        case 100: // Команда запуска демо через UART
            if (!Demo_Is_Active())
            {
                Demo_Start_Fire_Cycle();
                // Отправляем подтверждение
                Send_To_Main((uint8_t*)"$100OK;", 7);
                return 1; // Команда обработана
            }
            else
            {
                // Демо уже активно
                Send_To_Main((uint8_t*)"$100ER;", 7);
                return 1; // Команда обработана
            }
            break;
            
        case 101: // Команда остановки демо через UART
            if (Demo_Is_Active())
            {
                Demo_Stop();
                Send_To_Main((uint8_t*)"$101OK;", 7);
                return 1; // Команда обработана
            }
            else
            {
                Send_To_Main((uint8_t*)"$101ER;", 7);
                return 1; // Команда обработана
            }
            break;
            
        case 102: // Команда получения статуса демо
            {
                uint8_t response[7] = {'$', '1', '0', '2', 0, 0, ';'};
                response[4] = Demo_Is_Active() ? '1' : '0';
                response[5] = Demo_Get_Current_Step() + '0';
                Send_To_Main(response, 7);
                return 1; // Команда обработана
            }
            break;
            
        case 103: // Команда установки цели (азимут в следующих байтах)
            // Формат: $103AABB; где AA - азимут MSB, BB - азимут LSB
            // Реализация зависит от формата команды
            Send_To_Main((uint8_t*)"$103OK;", 7);
            return 1; // Команда обработана
            break;
            
        default:
            return 0; // Команда не обработана, передаем дальше
    }
}

// Функция включения/выключения интеграции демо
void Demo_Integration_Enable(uint8_t enable)
{
    demo_integration_enabled = enable;
    
    if (!enable && Demo_Is_Active())
    {
        Demo_Stop(); // Останавливаем демо при отключении интеграции
    }
}

// Функция получения статуса интеграции
uint8_t Demo_Integration_Is_Enabled(void)
{
    return demo_integration_enabled;
}

// Функция для отображения информации о демо на LCD
void Demo_Integration_Display_Info(void)
{
    if (!demo_integration_enabled) return;
    
    if (Demo_Is_Active())
    {
        // Отображаем текущий шаг демо
        LCD_Send_Command(LCD_1_LINE_POS_0);
        LCD_SendString((uint8_t *)"=== DEMO ACTIVE ===", 20);
        
        uint8_t step_info[21] = "Step: X/6           ";
        step_info[6] = Demo_Get_Current_Step() + '1'; // +1 для отображения 1-7 вместо 0-6
        
        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString(step_info, 20);
    }
    else
    {
        // Отображаем информацию о доступности демо
        static uint32_t blink_counter = 0;
        blink_counter++;
        
        if ((blink_counter % 1000) < 500) // Мигание каждые 500 итераций
        {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"Press ALL SW for DEMO", 20);
        }
        else
        {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"                    ", 20);
        }
    }
}

// Функция инициализации интеграции демо
void Demo_Integration_Init(void)
{
    demo_integration_enabled = 1;
    demo_check_counter = 0;
    
    // Устанавливаем параметры цели по умолчанию
    Demo_Set_Target(DEFAULT_TARGET_AZIMUTH, DEFAULT_TARGET_ELEVATION);
    
    // Отображаем информацию о готовности демо
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Demo Ready          ", 20);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
    Delay_mS(250);
}
