#ifndef MAIN_H
#define MAIN_H

#include "stm32f10x.h"
//#include "GPIO_STM32F10x.h"
#endif
#include "stdint.h"


#define SW1 GPIOB->IDR & GPIO_IDR_IDR8 //For checking SW1 state, pressed or depressed
#define SW2 GPIOB->IDR & GPIO_IDR_IDR9 //For checking SW2 state, pressed or depressed

#define SW3 GPIOB->IDR & GPIO_IDR_IDR12 //For checking SW3 state, pressed or depressed
#define SW4 GPIOB->IDR & GPIO_IDR_IDR13 //For checking SW4 state, pressed or depressed
#define SW5 GPIOB->IDR & GPIO_IDR_IDR14 //For checking SW5 state, pressed or depressed
#define SW6 GPIOB->IDR & GPIO_IDR_IDR15 //For checking SW6 state, pressed or depressed

//For sensors D1 - D14
#define D1 GPIOE->IDR & GPIO_IDR_IDR0 //For D1 State
#define D2 GPIOE->IDR & GPIO_IDR_IDR1 //For D2 State
#define D3 GPIOE->IDR & GPIO_IDR_IDR2 //For D3 State
#define D4 GPIOE->IDR & GPIO_IDR_IDR3 //For D4 State
#define D5 GPIOE->IDR & GPIO_IDR_IDR4 //For D5 State
#define D6 GPIOE->IDR & GPIO_IDR_IDR5 //For D6 State
#define D7 GPIOE->IDR & GPIO_IDR_IDR6 //For D7 State
#define D8 GPIOE->IDR & GPIO_IDR_IDR7 //For D8 State
#define D9 GPIOE->IDR & GPIO_IDR_IDR8 //For D9 State
#define D10 GPIOE->IDR & GPIO_IDR_IDR9 //For D10 State
#define D11 GPIOE->IDR & GPIO_IDR_IDR10 //For D11 State
#define D12 GPIOE->IDR & GPIO_IDR_IDR11 //For D12 State
#define D13 GPIOE->IDR & GPIO_IDR_IDR12 //For D13 State
#define D14 GPIOE->IDR & GPIO_IDR_IDR13 //For D14 State

//For Encoder Bus E0 - E9
#define E0 (GPIOD->IDR & GPIO_IDR_IDR0) //For E0 State
#define E1 (GPIOD->IDR & GPIO_IDR_IDR1) //For E1 State
#define E2 (GPIOD->IDR & GPIO_IDR_IDR2) //For E2 State
#define E3 (GPIOD->IDR & GPIO_IDR_IDR3) //For E3 State
#define E4 (GPIOD->IDR & GPIO_IDR_IDR4) //For E4 State
#define E5 (GPIOD->IDR & GPIO_IDR_IDR5) //For E5 State
#define E6 (GPIOD->IDR & GPIO_IDR_IDR6) //For E6 State
#define E7 (GPIOD->IDR & GPIO_IDR_IDR7) //For E7 State
#define E8 (GPIOD->IDR & GPIO_IDR_IDR8) //For E8 State
#define E9 (GPIOD->IDR & GPIO_IDR_IDR9) //For E9 State

#define Encoder1_Enable GPIOD->ODR &= ~(GPIO_ODR_ODR12)
#define Encoder1_Disable GPIOD->ODR |= GPIO_ODR_ODR12

#define Encoder2_Enable GPIOD->ODR &= ~(GPIO_ODR_ODR13)
#define Encoder2_Disable GPIOD->ODR |= GPIO_ODR_ODR13


#define BEEP_ON (GPIOC->ODR |= GPIO_ODR_ODR0)
#define BEEP_OFF GPIOC->ODR &= ~GPIO_ODR_ODR0

#define M7_Stop (GPIOB->ODR &= ~(GPIO_ODR_ODR10)); GPIOC->ODR &= ~GPIO_ODR_ODR3
//#define M7_GO_Left GPIOC->ODR |= GPIO_ODR_ODR2
#define M7_GO_Right GPIOC->ODR |= GPIO_ODR_ODR3
//#define M7_GO_Left GPIOC->ODR |= GPIO_ODR_ODR5
#define M7_GO_Left GPIOB->ODR |= GPIO_ODR_ODR10


#define DD16_Enble GPIOC->ODR |= GPIO_ODR_ODR1
#define DD16_Disble GPIOC->ODR &= ~(GPIO_ODR_ODR1)

#define Choose_M1 GPIOB->ODR |= GPIO_ODR_ODR3; GPIOB->ODR &= ~(GPIO_ODR_ODR4); GPIOB->ODR &= ~(GPIO_ODR_ODR5)
#define Choose_M2 GPIOB->ODR &= ~(GPIO_ODR_ODR3); GPIOB->ODR |= GPIO_ODR_ODR4; GPIOB->ODR &= ~(GPIO_ODR_ODR5)
#define Choose_M3 GPIOB->ODR |= GPIO_ODR_ODR3; GPIOB->ODR |= GPIO_ODR_ODR4; GPIOB->ODR &= ~(GPIO_ODR_ODR5) 
#define Choose_M4 GPIOB->ODR &= ~(GPIO_ODR_ODR3); GPIOB->ODR &= ~(GPIO_ODR_ODR4); GPIOB->ODR |= GPIO_ODR_ODR5 
#define Choose_M5 GPIOB->ODR |= GPIO_ODR_ODR3; GPIOB->ODR &= ~(GPIO_ODR_ODR4); GPIOB->ODR |= GPIO_ODR_ODR5  
#define Choose_M6 GPIOB->ODR &= ~(GPIO_ODR_ODR3); GPIOB->ODR |= GPIO_ODR_ODR4; GPIOB->ODR |= GPIO_ODR_ODR5  

#define Enable_Motor GPIOB->ODR &= ~(GPIO_ODR_ODR2)
#define Disable_Motor GPIOC->ODR |= GPIO_ODR_ODR2
#define Rotate_CW GPIOB->ODR &= ~(GPIO_ODR_ODR1)		
#define Rotate_CCW GPIOB->ODR |= GPIO_ODR_ODR1
									
									


void SetupGpioIO(void);
