# 🚀 CORDON-82 v15.5.0 PARALLEL EDITION

## 📋 **ОПИСАНИЕ**
Полная Arduino версия прошивки автоматизированной системы управления минометом CORDON-82.

### **✅ Особенности v15.0a:**
- **Трехуровневая архитектура**: Макросы → Микросценарии → Полные сценарии
- **ТУРБО режимы** для всех моторов M1-M6
- **I2C LCD** поддержка (20x4 с PCF8574)
- **M7 управление** через SW1+SW6
- **UART протокол** команд 0-16
- **Демо-сценарий** полного цикла стрельбы
- **Модульная структура** для легкого расширения

## 🔧 **АППАРАТНЫЕ ТРЕБОВАНИЯ**

### **Микроконтроллер:**
- **STM32F103ZE** (ARM Cortex-M3, 72MHz)
- **12MHz HSE кварц** (PLL x6 = 72MHz)
- **512KB Flash, 64KB RAM**

### **Периферия:**
- **LCD 20x4** с I2C адаптером (PCF8574)
- **7 моторов**: M1-M6 шаговые, M7 DC
- **6 кнопок**: SW1-SW6
- **14 датчиков**: D1-D14
- **2 энкодера**: E1-E2
- **Звуковой сигнализатор**

## 📁 **СТРУКТУРА ПРОЕКТА**

```
C:\14\15\
├── CORDON_82_v15_0a_Arduino.ino    # Основной файл Arduino
├── config.h                        # Центральная конфигурация
├── motor_control.h/cpp             # Управление моторами
├── motor_macros.h                  # Макросы движений (Уровень 1)
├── micro_scenarios.h/cpp           # Микросценарии (Уровень 2)
├── full_scenarios.h/cpp            # Полные сценарии (Уровень 3)
├── sensor_manager.h/cpp            # Управление датчиками
├── README_v15_0a.md               # Этот файл
└── CHANGELOG_v15_0a.md            # Список изменений
```

## 🎯 **АРХИТЕКТУРА КОДА**

### **Уровень 1: МАКРОСЫ**
Базовые движения моторов в разных скоростных режимах:
```cpp
// Примеры макросов (будут реализованы):
M1_STEP_TURBO(steps, direction)     // Турбо режим
M1_STEP_NORMAL(steps, direction)    // Нормальный режим
M1_STEP_SLOW(steps, direction)      // Медленный режим
```

### **Уровень 2: МИКРОСЦЕНАРИИ**
Логические блоки движений:
```cpp
// Примеры микросценариев:
M1_MOVE_D1_TO_D2_FAST()            // Движение от D1 до D2 максимальной скоростью
M3_LOAD_CYCLE()                     // Цикл подачи снаряда
M7_RAMMER_CYCLE()                   // Цикл досылателя
```

### **Уровень 3: ПОЛНЫЕ СЦЕНАРИИ**
Комплексные операции:
```cpp
// Примеры полных сценариев:
FULL_FIRE_CYCLE()                   // Полный цикл стрельбы
TARGET_ACQUISITION(azimuth, elev)   // Наведение на цель
EMERGENCY_STOP_ALL()                // Аварийная остановка
```

## ⚡ **СКОРОСТНЫЕ РЕЖИМЫ**

### **M1 (Азимут):**
- **ТУРБО**: 1мс+1мс (максимальная скорость)
- **НОРМАЛЬНЫЙ**: 5мс+5мс
- **МЕДЛЕННЫЙ**: 10мс+10мс (точное позиционирование)

### **M2 (Угол возвышения):**
- **ТУРБО**: 10мс+2мс (ускорен в 5 раз)
- **НОРМАЛЬНЫЙ**: 25мс+5мс
- **МЕДЛЕННЫЙ**: 50мс+10мс

### **M3 (Подача):**
- **ТУРБО**: 1мс+1мс (ускорен)
- **НОРМАЛЬНЫЙ**: 5мс+5мс
- **МЕДЛЕННЫЙ**: 10мс+10мс

### **M4 (Досылка):**
- **ТУРБО**: 50μS+50μS (ускорен в 2 раза)
- **НОРМАЛЬНЫЙ**: 100μS+100μS
- **МЕДЛЕННЫЙ**: 200μS+200μS

### **M5 (Фиксация):**
- **ТУРБО**: 1мс+1мс (в 3 раза быстрее)
- **НОРМАЛЬНЫЙ**: 3мс+3мс
- **МЕДЛЕННЫЙ**: 5мс+5мс

### **M6 (Барабан):**
- **МОЩНЫЙ**: 15мс+1мс (увеличена мощность)
- **НОРМАЛЬНЫЙ**: 8мс+2мс
- **МЕДЛЕННЫЙ**: 5мс+5мс

## 🎮 **УПРАВЛЕНИЕ**

### **Кнопки:**
- **SW1**: Тест мотора M1 (100 шагов ТУРБО)
- **SW2**: Тест мотора M2 (50 шагов ТУРБО)
- **SW3**: Тест мотора M3 (100 шагов ТУРБО)
- **SW4**: Тест мотора M4 (200 шагов ТУРБО)
- **SW5**: Тест мотора M5 (100 шагов ТУРБО)
- **SW6**: Тест мотора M6 (20 шагов МОЩНЫЙ)
- **SW1+SW6**: Управление M7 (СТОП→ВЛЕВО→ВПРАВО→СТОП)
- **Все кнопки**: Запуск демо-сценария

### **UART команды:**
```
CMD 0:  Reset - установка в исходное положение
CMD 1:  State - получение текущих позиций
CMD 2:  Azimuth CW - поворот азимута по часовой
CMD 3:  Azimuth CCW - поворот азимута против часовой
CMD 4:  Elevation UP - подъем ствола
CMD 5:  Elevation DOWN - опускание ствола
CMD 6:  Load Shell - подача снаряда
CMD 7:  Fire Cycle - полный цикл стрельбы
CMD 8-15: Управление моторами M3-M6
CMD 16: Управление M7
```

## 🔧 **НАСТРОЙКА I2C LCD**

### **Подключение:**
- **VCC** → 5V
- **GND** → GND
- **SDA** → PB7 (I2C1_SDA)
- **SCL** → PB6 (I2C1_SCL)

### **I2C адреса:**
- **Основной**: 0x27
- **Альтернативный**: 0x3F

### **Библиотека:**
```cpp
#include <LiquidCrystal_I2C.h>
LiquidCrystal_I2C lcd(0x27, 20, 4);
```

## 📋 **ПРАВИЛЬНАЯ РАСПИНОВКА (из IO_gpio.c):**

### **🎮 Кнопки управления:**
```
SW1 → PB8    SW2 → PB9    SW3 → PB12
SW4 → PB13   SW5 → PB14   SW6 → PB15
```

### **📊 Датчики D1-D14:**
```
D1  → PE0    D2  → PE1    D3  → PE2    D4  → PE3
D5  → PE4    D6  → PE5    D7  → PE6    D8  → PE7
D9  → PE8    D10 → PE9    D11 → PE10   D12 → PE11
D13 → PE12   D14 → PE13
```

### **🔧 Моторы M1-M6:**
```
M1_STEP → PB0       M1_DIR → PB1
M_ENABLE → PB2      DD16_ENABLE → PC1
M_SEL1 → PB3        M_SEL2 → PB4        M_SEL3 → PB5
```

### **⚡ Мотор M7 (DC):**
```
M7_LEFT → PB10      M7_RIGHT → PC3      M7_STOP → PC2
```

### **📊 Энкодеры:**
```
ENC1_ENABLE → PD12  ENC2_ENABLE → PD13
E0 → PD0   E1 → PD1   E2 → PD2   E3 → PD3   E4 → PD4
E5 → PD5   E6 → PD6   E7 → PD7   E8 → PD8   E9 → PD9
```

### **🔊 Звук и индикация:**
```
BEEP → PC0          LED → PC13
```

### **📡 Связь:**
```
UART1: TX → PA9, RX → PA10
UART2: TX → PA2, RX → PA3
I2C1:  SCL → PB6, SDA → PB7
```

### **📈 Аналоговые входы:**
```
M7_CURRENT → PA0    BATTERY_VOLTAGE → PA1
```

## 📚 **НЕОБХОДИМЫЕ БИБЛИОТЕКИ**

### **Для Arduino IDE:**
1. **LiquidCrystal_I2C** - для I2C LCD
2. **Wire** - для I2C (встроенная)

### **Для PlatformIO:**
```ini
lib_deps = 
    marcoschwartz/LiquidCrystal_I2C@^1.1.4
```

## 🚀 **КОМПИЛЯЦИЯ И ЗАГРУЗКА**

### **Arduino IDE:**
1. Установить поддержку STM32: `https://github.com/stm32duino/Arduino_Core_STM32`
2. Выбрать плату: `STM32F1 series → Generic STM32F103ZE`
3. Выбрать загрузчик: `STM32CubeProgrammer (SWD)`
4. Компилировать и загрузить

### **PlatformIO:**
```ini
[env:genericSTM32F103ZE]
platform = ststm32
board = genericSTM32F103ZE
framework = arduino
upload_protocol = stlink
```

## 🎯 **ПЛАН РАЗВИТИЯ**

### **Этап 1: Базовая функциональность** ✅
- [x] Минимальная рабочая версия
- [x] I2C LCD поддержка
- [x] Базовое управление моторами
- [x] M7 управление через SW1+SW6

### **Этап 2: Макросы** (в разработке)
- [ ] Макросы движений для всех моторов
- [ ] Разные скоростные режимы
- [ ] Проверка датчиков

### **Этап 3: Микросценарии** (планируется)
- [ ] Движения между датчиками
- [ ] Циклы заряжания
- [ ] Наведение на цель

### **Этап 4: Полные сценарии** (планируется)
- [ ] Полный цикл стрельбы
- [ ] UART протокол
- [ ] Демо-режим

## 🐛 **ОТЛАДКА**

### **Serial Monitor:**
- **Скорость**: 115200 baud
- **Отладочные сообщения**: включены
- **Статус инициализации**: отображается

### **LCD индикация:**
- **Строка 1**: Название системы
- **Строка 2**: Версия прошивки
- **Строка 3**: Статус системы
- **Строка 4**: Текущая операция

## 📞 **ПОДДЕРЖКА**

При возникновении проблем проверьте:
1. **I2C подключение** LCD
2. **Питание** 5V для LCD
3. **I2C адрес** (0x27 или 0x3F)
4. **Библиотеки** установлены
5. **Плата** выбрана правильно

---
**CORDON-82 v15.0a Arduino Edition** - Современная, модульная, расширяемая система! 🚀
