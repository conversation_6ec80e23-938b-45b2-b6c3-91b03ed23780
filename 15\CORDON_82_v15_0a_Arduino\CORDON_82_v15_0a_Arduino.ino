/*
 * CORDON-82 v15.5.0 PARALLEL EDITION
 * Automated Mortar Control System
 *
 * ПОЛНАЯ ВЕРСИЯ С ПАРАЛЛЕЛЬНОСТЬЮ - ВРЕМЯ ЦИКЛА СОКРАЩЕНО В 2 РАЗА!
 *
 * Hardware: STM32F103ZE
 * Clock: 72MHz (12MHz HSE * 6)
 * Распиновка: ИСПРАВЛЕНА из IO_gpio.c
 *
 * Author: Augment Agent
 * Date: 2025
 * Version: 15.5.0 PARALLEL EDITION
 */

#include "config.h"
#include <Wire.h>
#include <LiquidCrystal_I2C.h>

// Подключение модулей v15.0a
#include "motor_control.h"
#include "motor_macros.h"
#include "micro_scenarios.h"
#include "sensor_manager.h"
#include "full_scenarios.h"

// ===== LCD CONFIGURATION =====
// LCD I2C: адрес, колонки, строки
LiquidCrystal_I2C lcd(LCD_I2C_ADDR, LCD_COLS, LCD_ROWS);

// ===== PIN DEFINITIONS MOVED TO config.h =====

// ===== MOTOR SPEED CONFIGURATION (TURBO MODE) =====
struct MotorSpeed {
    int pulse_ms;
    int pause_ms;
    bool use_microseconds;
};

MotorSpeed motorSpeeds[7] = {
    {0, 0, false},      // M0 (не используется)
    {1, 1, false},      // M1 ТУРБО (1мс+1мс) - СУПЕР
    {10, 2, false},     // M2 УСКОРЕН (10мс+2мс) - в 5 раз быстрее
    {1, 1, false},      // M3 ТУРБО (1мс+1мс) - ускорен
    {50, 50, true},     // M4 УСКОРЕН (50μS+50μS) - в 2 раза быстрее
    {1, 1, false},      // M5 ТУРБО (1мс+1мс) - в 3 раза быстрее
    {15, 1, false}      // M6 МОЩНЫЙ (15мс+1мс) - увеличена мощность
};

// ===== GLOBAL VARIABLES =====
uint8_t demo_active = 0;
uint8_t demo_step = 0;
uint8_t m7_state = 0; // 0=stop, 1=left, 2=right
uint8_t uart_cmd_buffer[7];
uint8_t uart_cmd_ready = 0;
uint8_t projectile_number = 0;
uint8_t sensor_error = 0;
uint8_t m7_error = 0;

uint16_t m1_angle = 0;
uint16_t m2_angle = 0;
uint16_t target_azimuth = 180;
uint16_t target_elevation = 45;

// ===== SETUP FUNCTION =====
void setup() {
    // Initialize Serial
    Serial.begin(115200);
    Serial.println("CORDON-82 v15.0a Arduino Edition Starting...");
    
    // Initialize I2C
    Serial.println("Initializing I2C...");
    Wire.begin();

    // Initialize LCD I2C
    Serial.println("Initializing LCD I2C...");
    lcd.init();
    lcd.backlight();
    lcd.clear();
    
    // Startup screen
    lcd.setCursor(0, 0);
    lcd.print("===== CORDON-82 ====");
    lcd.setCursor(0, 1);
    lcd.print("Arduino v15.0a TURBO");
    lcd.setCursor(0, 2);
    lcd.print("Clock: 72MHz HSE");
    lcd.setCursor(0, 3);
    lcd.print("Initializing...");
    
    // Initialize motor pins
    pinMode(M1_STEP, OUTPUT);
    pinMode(M1_DIR, OUTPUT);
    pinMode(M_ENABLE, OUTPUT);
    pinMode(M_SEL1, OUTPUT);
    pinMode(M_SEL2, OUTPUT);
    pinMode(M_SEL3, OUTPUT);
    pinMode(DD16_ENABLE, OUTPUT);
    
    // Initialize M7 pins
    pinMode(M7_LEFT, OUTPUT);
    pinMode(M7_RIGHT, OUTPUT);
    pinMode(M7_STOP, OUTPUT);
    
    // Initialize button pins
    pinMode(SW1, INPUT_PULLUP);
    pinMode(SW2, INPUT_PULLUP);
    pinMode(SW3, INPUT_PULLUP);
    pinMode(SW4, INPUT_PULLUP);
    pinMode(SW5, INPUT_PULLUP);
    pinMode(SW6, INPUT_PULLUP);
    
    // Initialize beeper
    pinMode(BEEP, OUTPUT);
    
    // Initialize sensors using module
    Serial.println("Initializing sensors...");
    initializeSensors();
    Serial.println("Sensors initialized OK");

    // Initialize motors using module
    Serial.println("Initializing motors...");
    initializeMotors();
    Serial.println("Motors initialized OK");
    
    // Startup beep sequence
    beep(250);
    delay(250);
    
    // Board self test display
    lcd.setCursor(0, 2);
    lcd.print("Board self testing  ");
    lcd.setCursor(0, 3);
    lcd.print("Please wait...      ");
    
    // Progress bar simulation
    for (int i = 0; i < 20; i++) {
        lcd.setCursor(i, 3);
        lcd.print("-");
        beep(50);
        delay(100);
    }
    
    // Ready screen
    lcd.setCursor(0, 2);
    lcd.print("TURBO Mode Ready!   ");
    lcd.setCursor(0, 3);
    lcd.print("Wait for Command    ");
    
    Serial.println("CORDON-82 v15.0a Ready!");
    Serial.println("Features: TURBO motors, M7 control, Demo scenario");
    Serial.println("Controls: SW1-SW6 buttons, SW1+SW6 for M7");
}

// ===== MAIN LOOP =====
void loop() {
    // Process button inputs
    processButtons();
    
    // Process UART commands
    processUART();
    
    // Process demo scenario
    processDemoScenario();
    
    // Small delay for main loop
    delay(10);
}

// ===== MOTOR FUNCTIONS MOVED TO motor_control.cpp =====
// Функции моторов теперь в отдельных модулях

void handleM7Control() {
    switch(m7_state) {
        case 0: // Stop -> Left
            m7GoLeft();
            lcd.setCursor(0, 3);
            lcd.print("M7 Going LEFT       ");
            m7_state = 1;
            break;

        case 1: // Left -> Right
            m7GoRight();
            lcd.setCursor(0, 3);
            lcd.print("M7 Going RIGHT      ");
            m7_state = 2;
            break;

        case 2: // Right -> Stop
            m7Stop();
            lcd.setCursor(0, 3);
            lcd.print("M7 STOPPED          ");
            m7_state = 0;
            break;
    }

    beep(BEEP_LONG);

    // Wait for button release
    while (!digitalRead(SW1) || !digitalRead(SW6)) {
        delay(10);
    }
    delay(DELAY_DEBOUNCE);
}

// ===== UTILITY FUNCTIONS =====
void beep(int duration) {
    digitalWrite(BEEP, HIGH);
    delay(duration);
    digitalWrite(BEEP, LOW);
}

void beepOK() {
    beep(100);
    delay(100);
    beep(100);
    delay(100);
    beep(200);
}

void beepError() {
    beep(250);
    delay(150);
    beep(100);
    delay(250);
    beep(250);
    delay(100);
    beep(250);
    delay(100);
    beep(250);
}

// ===== BUTTON PROCESSING =====
void processButtons() {
    // Проверка комбинации SW1+SW6 для M7
    if (!digitalRead(SW1) && !digitalRead(SW6)) {
        handleM7Control();
        return;
    }

    // Проверка всех кнопок одновременно для демо-сценария
    if (!digitalRead(SW1) && !digitalRead(SW2) && !digitalRead(SW3) &&
        !digitalRead(SW4) && !digitalRead(SW5) && !digitalRead(SW6)) {
        startDemoScenario();
        return;
    }

    // Проверка отдельных кнопок для тестов моторов
    if (!digitalRead(SW1)) {
        lcd.setCursor(0, 3);
        lcd.print("Testing M1 TURBO    ");
        beep(BEEP_SHORT);
        testMotor(1);
        delay(DELAY_DEBOUNCE);
    }

    if (!digitalRead(SW2)) {
        lcd.setCursor(0, 3);
        lcd.print("Testing M2 TURBO    ");
        beep(BEEP_SHORT);
        testMotor(2);
        delay(DELAY_DEBOUNCE);
    }

    if (!digitalRead(SW3)) {
        lcd.setCursor(0, 3);
        lcd.print("Testing M3 TURBO    ");
        beep(BEEP_SHORT);
        testMotor(3);
        delay(DELAY_DEBOUNCE);
    }

    if (!digitalRead(SW4)) {
        lcd.setCursor(0, 3);
        lcd.print("Testing M4 TURBO    ");
        beep(BEEP_SHORT);
        testMotor(4);
        delay(DELAY_DEBOUNCE);
    }

    if (!digitalRead(SW5)) {
        lcd.setCursor(0, 3);
        lcd.print("Testing M5 TURBO    ");
        beep(BEEP_SHORT);
        testMotor(5);
        delay(DELAY_DEBOUNCE);
    }

    if (!digitalRead(SW6)) {
        lcd.setCursor(0, 3);
        lcd.print("Testing M6 POWER    ");
        beep(BEEP_SHORT);
        testMotor(6);
        delay(DELAY_DEBOUNCE);
    }
}

// ===== UART PROCESSING =====
void processUART() {
    // Заглушка для UART протокола
    if (Serial.available() > 0) {
        String input = Serial.readString();
        input.trim();
        Serial.println("UART received: " + input);

        // Команды для тестирования и управления
        if (input == "test") {
            TEST_ALL_MOTORS_SEQUENCE();
        } else if (input == "demo") {
            FULL_DEMONSTRATION();
        } else if (input == "fire") {
            FULL_FIRE_CYCLE(90, 45); // Демо-стрельба
        } else if (input == "home") {
            RETURN_TO_HOME_POSITION();
        } else if (input == "load") {
            COMPLETE_LOADING_SEQUENCE();
        } else if (input == "target") {
            PRECISION_TARGETING(180, 30);
        } else if (input == "status") {
            printMotorStatus();
            printSensorStatus();
        } else if (input == "emergency") {
            EMERGENCY_SHUTDOWN();
        } else if (input == "help") {
            Serial.println("Available commands:");
            Serial.println("test - Test all motors");
            Serial.println("demo - Full demonstration");
            Serial.println("fire - Fire cycle demo");
            Serial.println("home - Return to home");
            Serial.println("load - Loading sequence");
            Serial.println("target - Precision targeting");
            Serial.println("status - System status");
            Serial.println("emergency - Emergency stop");
        }
    }
}

// ===== DEMO SCENARIO =====
void processDemoScenario() {
    // Заглушка для демо-сценария
    // Будет реализовано позже
}

void startDemoScenario() {
    lcd.setCursor(0, 3);
    lcd.print("DEMO SCENARIO START ");
    beepOK();

    Serial.println("=== DEMO SCENARIO STARTED ===");

    // Выполнить полную демонстрацию
    FULL_DEMONSTRATION();

    lcd.setCursor(0, 3);
    lcd.print("DEMO COMPLETED      ");
    beepOK();

    Serial.println("=== DEMO SCENARIO COMPLETED ===");
}
