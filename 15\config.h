/*
 * CORDON-82 v15.0a Arduino Edition - Configuration
 * Центральная конфигурация системы
 */

#ifndef CONFIG_H
#define CONFIG_H

// ===== ВЕРСИЯ ПРОШИВКИ =====
#define FIRMWARE_VERSION "15.0a Arduino Edition"
#define FIRMWARE_DATE "2025-01-XX"

// ===== PIN DEFINITIONS =====

// Motor control pins
#define M1_STEP     PB0
#define M1_DIR      PB1
#define M_ENABLE    PB2
#define M_SEL1      PB3
#define M_SEL2      PB4
#define M_SEL3      PB5

// M7 DC Motor pins
#define M7_LEFT     PB6
#define M7_RIGHT    PB7
#define M7_STOP     PB8

// Button pins
#define SW1         PC8
#define SW2         PC9
#define SW3         PC10
#define SW4         PC11
#define SW5         PC12
#define SW6         PC13

// Beeper pin
#define BEEP        PC14

// DD16 enable pin
#define DD16_ENABLE PC1

// LCD I2C settings
#define LCD_I2C_ADDR    0x27    // Стандартный адрес PCF8574
#define LCD_I2C_ADDR_ALT 0x3F   // Альтернативный адрес
#define LCD_SDA         PB7     // I2C1 SDA pin
#define LCD_SCL         PB6     // I2C1 SCL pin

// Sensor pins D1-D14
#define D1          PD0
#define D2          PD1
#define D3          PD2
#define D4          PD3
#define D5          PD4
#define D6          PD5
#define D7          PD6
#define D8          PD7
#define D9          PD8
#define D10         PD9
#define D11         PD10
#define D12         PD11
#define D13         PD12
#define D14         PD13

// Encoder enable pins
#define ENC1_ENABLE PD14
#define ENC2_ENABLE PD15

// ===== MOTOR DEFINITIONS =====
#define MOTOR_M1    1
#define MOTOR_M2    2
#define MOTOR_M3    3
#define MOTOR_M4    4
#define MOTOR_M5    5
#define MOTOR_M6    6
#define MOTOR_M7    7

// Direction definitions
#define DIR_CW      1
#define DIR_CCW     0
#define DIR_FORWARD 1
#define DIR_BACK    0

// ===== SPEED PROFILES =====

// M1 Speed profiles (Азимут)
#define M1_TURBO_PULSE    1     // 1мс - СУПЕР скорость
#define M1_TURBO_PAUSE    1     // 1мс
#define M1_NORMAL_PULSE   5     // 5мс - нормальная
#define M1_NORMAL_PAUSE   5     // 5мс
#define M1_SLOW_PULSE     10    // 10мс - медленная
#define M1_SLOW_PAUSE     10    // 10мс

// M2 Speed profiles (Угол возвышения)
#define M2_TURBO_PULSE    10    // 10мс - ускорен в 5 раз
#define M2_TURBO_PAUSE    2     // 2мс
#define M2_NORMAL_PULSE   25    // 25мс - нормальная
#define M2_NORMAL_PAUSE   5     // 5мс
#define M2_SLOW_PULSE     50    // 50мс - медленная
#define M2_SLOW_PAUSE     10    // 10мс

// M3 Speed profiles (Подача)
#define M3_TURBO_PULSE    1     // 1мс - ускорен
#define M3_TURBO_PAUSE    1     // 1мс
#define M3_NORMAL_PULSE   5     // 5мс - нормальная
#define M3_NORMAL_PAUSE   5     // 5мс
#define M3_SLOW_PULSE     10    // 10мс - медленная
#define M3_SLOW_PAUSE     10    // 10мс

// M4 Speed profiles (Досылка) - в микросекундах!
#define M4_TURBO_PULSE    50    // 50μS - ускорен в 2 раза
#define M4_TURBO_PAUSE    50    // 50μS
#define M4_NORMAL_PULSE   100   // 100μS - нормальная
#define M4_NORMAL_PAUSE   100   // 100μS
#define M4_SLOW_PULSE     200   // 200μS - медленная
#define M4_SLOW_PAUSE     200   // 200μS

// M5 Speed profiles (Фиксация)
#define M5_TURBO_PULSE    1     // 1мс - в 3 раза быстрее
#define M5_TURBO_PAUSE    1     // 1мс
#define M5_NORMAL_PULSE   3     // 3мс - нормальная
#define M5_NORMAL_PAUSE   3     // 3мс
#define M5_SLOW_PULSE     5     // 5мс - медленная
#define M5_SLOW_PAUSE     5     // 5мс

// M6 Speed profiles (Барабан) - увеличена мощность
#define M6_POWER_PULSE    15    // 15мс - мощный режим
#define M6_POWER_PAUSE    1     // 1мс
#define M6_NORMAL_PULSE   8     // 8мс - нормальная
#define M6_NORMAL_PAUSE   2     // 2мс
#define M6_SLOW_PULSE     5     // 5мс - медленная
#define M6_SLOW_PAUSE     5     // 5мс

// ===== STEP COUNTS =====
#define STEPS_SMALL     10      // Малое движение
#define STEPS_MEDIUM    50      // Среднее движение
#define STEPS_LARGE     100     // Большое движение
#define STEPS_FULL      200     // Полное движение

// ===== TIMING CONSTANTS =====
#define BEEP_SHORT      100     // Короткий сигнал
#define BEEP_LONG       250     // Длинный сигнал
#define DELAY_DEBOUNCE  500     // Дебаунс кнопок
#define DELAY_MOTOR     5       // Задержка между моторами
#define DELAY_MAIN_LOOP 10      // Основной цикл

// ===== UART PROTOCOL =====
#define UART_BAUD       115200
#define UART_CMD_SIZE   7

// Command numbers
#define CMD_RESET       0
#define CMD_STATE       1
#define CMD_AZIMUTH_CW  2
#define CMD_AZIMUTH_CCW 3
#define CMD_ELEV_UP     4
#define CMD_ELEV_DOWN   5
#define CMD_LOAD_SHELL  6
#define CMD_FIRE_CYCLE  7
#define CMD_M3_FORWARD  8
#define CMD_M3_BACK     9
#define CMD_M4_FORWARD  10
#define CMD_M4_BACK     11
#define CMD_M5_FORWARD  12
#define CMD_M5_BACK     13
#define CMD_M6_FORWARD  14
#define CMD_M6_BACK     15
#define CMD_M7_CONTROL  16

// ===== LCD CONSTANTS =====
#define LCD_COLS        20
#define LCD_ROWS        4

// LCD line positions
#define LCD_LINE_1      0
#define LCD_LINE_2      1
#define LCD_LINE_3      2
#define LCD_LINE_4      3

// ===== SAFETY CONSTANTS =====
#define MAX_MOTOR_TIMEOUT   5000    // 5 секунд максимум для операции
#define EMERGENCY_STOP_TIME 100     // 100мс на аварийную остановку

// ===== DEBUG SETTINGS =====
#define DEBUG_ENABLED   1           // Включить отладку
#define DEBUG_MOTORS    1           // Отладка моторов
#define DEBUG_UART      1           // Отладка UART
#define DEBUG_BUTTONS   1           // Отладка кнопок

#endif // CONFIG_H
