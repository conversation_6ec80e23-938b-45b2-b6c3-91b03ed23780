/*
 * CORDON-82 v15.0a Arduino Edition - Configuration
 * Центральная конфигурация системы
 */

#ifndef CONFIG_H
#define CONFIG_H

// ===== ВЕРСИЯ ПРОШИВКИ =====
#define FIRMWARE_VERSION "15.0a Arduino Edition"
#define FIRMWARE_DATE "2025-01-XX"

// ===== PIN DEFINITIONS =====

// Motor control pins
#define M1_STEP     PB0
#define M1_DIR      PB1
#define M_ENABLE    PB2
#define M_SEL1      PB3
#define M_SEL2      PB4
#define M_SEL3      PB5

// Button pins (ИСПРАВЛЕНО из IO_gpio.c!)
#define SW1         PB8     // Было PC8 - НЕПРАВИЛЬНО!
#define SW2         PB9     // Было PC9 - НЕПРАВИЛЬНО!
#define SW3         PB12    // Было PC10 - НЕПРАВИЛЬНО!
#define SW4         PB13    // Было PC11 - НЕПРАВИЛЬНО!
#define SW5         PB14    // Было PC12 - НЕПРАВИЛЬНО!
#define SW6         PB15    // Было PC13 - НЕПРАВИЛЬНО!

// Beeper pin (ИСПРАВЛЕНО!)
#define BEEP        PC0     // Было PC14 - НЕПРАВИЛЬНО!

// DD16 enable pin (ПРАВИЛЬНО!)
#define DD16_ENABLE PC1

// LCD I2C settings (I2C1 на PB6/PB7)
#define LCD_I2C_ADDR    0x27    // Стандартный адрес PCF8574
#define LCD_I2C_ADDR_ALT 0x3F   // Альтернативный адрес
#define LCD_SDA         PB7     // I2C1 SDA pin (ПРАВИЛЬНО!)
#define LCD_SCL         PB6     // I2C1 SCL pin (ПРАВИЛЬНО!)

// Sensor pins D1-D14 (ИСПРАВЛЕНО из IO_gpio.c!)
#define D1          PE0     // Было PD0 - НЕПРАВИЛЬНО!
#define D2          PE1     // Было PD1 - НЕПРАВИЛЬНО!
#define D3          PE2     // Было PD2 - НЕПРАВИЛЬНО!
#define D4          PE3     // Было PD3 - НЕПРАВИЛЬНО!
#define D5          PE4     // Было PD4 - НЕПРАВИЛЬНО!
#define D6          PE5     // Было PD5 - НЕПРАВИЛЬНО!
#define D7          PE6     // Было PD6 - НЕПРАВИЛЬНО!
#define D8          PE7     // Было PD7 - НЕПРАВИЛЬНО!
#define D9          PE8     // Было PD8 - НЕПРАВИЛЬНО!
#define D10         PE9     // Было PD9 - НЕПРАВИЛЬНО!
#define D11         PE10    // Было PD10 - НЕПРАВИЛЬНО!
#define D12         PE11    // Было PD11 - НЕПРАВИЛЬНО!
#define D13         PE12    // Было PD12 - НЕПРАВИЛЬНО!
#define D14         PE13    // Было PD13 - НЕПРАВИЛЬНО!

// Encoder enable pins (ИСПРАВЛЕНО!)
#define ENC1_ENABLE PD12    // Было PD14 - НЕПРАВИЛЬНО!
#define ENC2_ENABLE PD13    // Было PD15 - НЕПРАВИЛЬНО!

// Encoder data pins E0-E11 (из IO_gpio.c)
#define E0          PD0
#define E1          PD1
#define E2          PD2
#define E3          PD3
#define E4          PD4
#define E5          PD5
#define E6          PD6
#define E7          PD7
#define E8          PD8
#define E9          PD9
#define E10         PD10    // Reserved
#define E11         PD11    // Reserved

// M7 DC Motor pins (ИСПРАВЛЕНО из IO_gpio.c!)
#define M7_LEFT     PB10    // Из кода: GPIOB->ODR &= ~(GPIO_ODR_ODR10)
#define M7_RIGHT    PC3     // Из кода: GPIOC->ODR &= ~(GPIO_ODR_ODR3)
#define M7_STOP     PC2     // Предположительно (нужно проверить)

// Analog inputs (ДОБАВЛЕНО!)
#define M7_CURRENT_ADC  PA0 // Motor M7 Current sensor
#define BATTERY_VOLTAGE PA1 // Battery Voltage sensor

// Driver alarms (ДОБАВЛЕНО!)
#define ALARM_1     PA4
#define ALARM_3     PA6
#define ALARM_4     PA7
#define ALARM_5     PA8
#define ALARM_6     PA11
#define ALARM_7     PA12

// ===== MOTOR DEFINITIONS =====
#define MOTOR_M1    1
#define MOTOR_M2    2
#define MOTOR_M3    3
#define MOTOR_M4    4
#define MOTOR_M5    5
#define MOTOR_M6    6
#define MOTOR_M7    7

// Direction definitions
#define DIR_CW      1
#define DIR_CCW     0
#define DIR_FORWARD 1
#define DIR_BACK    0

// ===== SPEED PROFILES =====

// M1 Speed profiles (Азимут)
#define M1_TURBO_PULSE    1     // 1мс - СУПЕР скорость
#define M1_TURBO_PAUSE    1     // 1мс
#define M1_NORMAL_PULSE   5     // 5мс - нормальная
#define M1_NORMAL_PAUSE   5     // 5мс
#define M1_SLOW_PULSE     10    // 10мс - медленная
#define M1_SLOW_PAUSE     10    // 10мс

// M2 Speed profiles (Угол возвышения)
#define M2_TURBO_PULSE    10    // 10мс - ускорен в 5 раз
#define M2_TURBO_PAUSE    2     // 2мс
#define M2_NORMAL_PULSE   25    // 25мс - нормальная
#define M2_NORMAL_PAUSE   5     // 5мс
#define M2_SLOW_PULSE     50    // 50мс - медленная
#define M2_SLOW_PAUSE     10    // 10мс

// M3 Speed profiles (Подача)
#define M3_TURBO_PULSE    1     // 1мс - ускорен
#define M3_TURBO_PAUSE    1     // 1мс
#define M3_NORMAL_PULSE   5     // 5мс - нормальная
#define M3_NORMAL_PAUSE   5     // 5мс
#define M3_SLOW_PULSE     10    // 10мс - медленная
#define M3_SLOW_PAUSE     10    // 10мс

// M4 Speed profiles (Досылка) - в микросекундах!
#define M4_TURBO_PULSE    50    // 50μS - ускорен в 2 раза
#define M4_TURBO_PAUSE    50    // 50μS
#define M4_NORMAL_PULSE   100   // 100μS - нормальная
#define M4_NORMAL_PAUSE   100   // 100μS
#define M4_SLOW_PULSE     200   // 200μS - медленная
#define M4_SLOW_PAUSE     200   // 200μS

// M5 Speed profiles (Фиксация)
#define M5_TURBO_PULSE    1     // 1мс - в 3 раза быстрее
#define M5_TURBO_PAUSE    1     // 1мс
#define M5_NORMAL_PULSE   3     // 3мс - нормальная
#define M5_NORMAL_PAUSE   3     // 3мс
#define M5_SLOW_PULSE     5     // 5мс - медленная
#define M5_SLOW_PAUSE     5     // 5мс

// M6 Speed profiles (Барабан) - увеличена мощность
#define M6_POWER_PULSE    15    // 15мс - мощный режим
#define M6_POWER_PAUSE    1     // 1мс
#define M6_NORMAL_PULSE   8     // 8мс - нормальная
#define M6_NORMAL_PAUSE   2     // 2мс
#define M6_SLOW_PULSE     5     // 5мс - медленная
#define M6_SLOW_PAUSE     5     // 5мс

// ===== STEP COUNTS =====
#define STEPS_SMALL     10      // Малое движение
#define STEPS_MEDIUM    50      // Среднее движение
#define STEPS_LARGE     100     // Большое движение
#define STEPS_FULL      200     // Полное движение

// ===== TIMING CONSTANTS =====
#define BEEP_SHORT      100     // Короткий сигнал
#define BEEP_LONG       250     // Длинный сигнал
#define DELAY_DEBOUNCE  500     // Дебаунс кнопок
#define DELAY_MOTOR     5       // Задержка между моторами
#define DELAY_MAIN_LOOP 10      // Основной цикл

// ===== UART PROTOCOL =====
#define UART_BAUD       115200
#define UART_CMD_SIZE   7

// Command numbers
#define CMD_RESET       0
#define CMD_STATE       1
#define CMD_AZIMUTH_CW  2
#define CMD_AZIMUTH_CCW 3
#define CMD_ELEV_UP     4
#define CMD_ELEV_DOWN   5
#define CMD_LOAD_SHELL  6
#define CMD_FIRE_CYCLE  7
#define CMD_M3_FORWARD  8
#define CMD_M3_BACK     9
#define CMD_M4_FORWARD  10
#define CMD_M4_BACK     11
#define CMD_M5_FORWARD  12
#define CMD_M5_BACK     13
#define CMD_M6_FORWARD  14
#define CMD_M6_BACK     15
#define CMD_M7_CONTROL  16

// ===== LCD CONSTANTS =====
#define LCD_COLS        20
#define LCD_ROWS        4

// LCD line positions
#define LCD_LINE_1      0
#define LCD_LINE_2      1
#define LCD_LINE_3      2
#define LCD_LINE_4      3

// ===== SAFETY CONSTANTS =====
#define MAX_MOTOR_TIMEOUT   5000    // 5 секунд максимум для операции
#define EMERGENCY_STOP_TIME 100     // 100мс на аварийную остановку

// ===== DEBUG SETTINGS =====
#define DEBUG_ENABLED   1           // Включить отладку
#define DEBUG_MOTORS    1           // Отладка моторов
#define DEBUG_UART      1           // Отладка UART
#define DEBUG_BUTTONS   1           // Отладка кнопок

#endif // CONFIG_H
