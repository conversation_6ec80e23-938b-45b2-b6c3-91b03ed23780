/*
 * CORDON-82 v15.0a Arduino Edition
 * Automated Mortar Control System
 * 
 * Features:
 * - 7 Motors (M1-M6 stepper, M7 DC)
 * - TURBO speed modes
 * - SW1+SW6 M7 control
 * - Full UART protocol
 * - Demo fire cycle
 * - LCD 20x4 interface
 * 
 * Hardware: STM32F103ZE
 * Clock: 72MHz (12MHz HSE * 6)
 * 
 * Author: Augment Agent
 * Date: 2025
 * Version: 15.0a Arduino Edition
 */

#include <LiquidCrystal.h>

// ===== LCD CONFIGURATION =====
// LCD pins: RS, EN, D4, D5, D6, D7
LiquidCrystal lcd(PE0, PE1, PE4, PE5, PE6, PE7);

// ===== PIN DEFINITIONS =====

// Motor control pins
#define M1_STEP     PB0
#define M1_DIR      PB1
#define M_ENABLE    PB2
#define M_SEL1      PB3
#define M_SEL2      PB4
#define M_SEL3      PB5

// M7 DC Motor pins
#define M7_LEFT     PB6
#define M7_RIGHT    PB7
#define M7_STOP     PB8

// Button pins
#define SW1         PC8
#define SW2         PC9
#define SW3         PC10
#define SW4         PC11
#define SW5         PC12
#define SW6         PC13

// Beeper pin
#define BEEP        PC14

// DD16 enable pin
#define DD16_ENABLE PC1

// Sensor pins D1-D14
#define D1          PD0
#define D2          PD1
#define D3          PD2
#define D4          PD3
#define D5          PD4
#define D6          PD5
#define D7          PD6
#define D8          PD7
#define D9          PD8
#define D10         PD9
#define D11         PD10
#define D12         PD11
#define D13         PD12
#define D14         PD13

// Encoder enable pins
#define ENC1_ENABLE PD14
#define ENC2_ENABLE PD15

// ===== MOTOR SPEED CONFIGURATION (TURBO MODE) =====
struct MotorSpeed {
    int pulse_ms;
    int pause_ms;
    bool use_microseconds;
};

MotorSpeed motorSpeeds[7] = {
    {0, 0, false},      // M0 (не используется)
    {1, 1, false},      // M1 ТУРБО (1мс+1мс) - СУПЕР
    {10, 2, false},     // M2 УСКОРЕН (10мс+2мс) - в 5 раз быстрее
    {1, 1, false},      // M3 ТУРБО (1мс+1мс) - ускорен
    {50, 50, true},     // M4 УСКОРЕН (50μS+50μS) - в 2 раза быстрее
    {1, 1, false},      // M5 ТУРБО (1мс+1мс) - в 3 раза быстрее
    {15, 1, false}      // M6 МОЩНЫЙ (15мс+1мс) - увеличена мощность
};

// ===== GLOBAL VARIABLES =====
uint8_t demo_active = 0;
uint8_t demo_step = 0;
uint8_t m7_state = 0; // 0=stop, 1=left, 2=right
uint8_t uart_cmd_buffer[7];
uint8_t uart_cmd_ready = 0;
uint8_t projectile_number = 0;
uint8_t sensor_error = 0;
uint8_t m7_error = 0;

uint16_t m1_angle = 0;
uint16_t m2_angle = 0;
uint16_t target_azimuth = 180;
uint16_t target_elevation = 45;

// ===== SETUP FUNCTION =====
void setup() {
    // Initialize Serial
    Serial.begin(115200);
    Serial.println("CORDON-82 v15.0a Arduino Edition Starting...");
    
    // Initialize LCD
    lcd.begin(20, 4);
    lcd.clear();
    
    // Startup screen
    lcd.setCursor(0, 0);
    lcd.print("===== CORDON-82 ====");
    lcd.setCursor(0, 1);
    lcd.print("Arduino v15.0a TURBO");
    lcd.setCursor(0, 2);
    lcd.print("Clock: 72MHz HSE");
    lcd.setCursor(0, 3);
    lcd.print("Initializing...");
    
    // Initialize motor pins
    pinMode(M1_STEP, OUTPUT);
    pinMode(M1_DIR, OUTPUT);
    pinMode(M_ENABLE, OUTPUT);
    pinMode(M_SEL1, OUTPUT);
    pinMode(M_SEL2, OUTPUT);
    pinMode(M_SEL3, OUTPUT);
    pinMode(DD16_ENABLE, OUTPUT);
    
    // Initialize M7 pins
    pinMode(M7_LEFT, OUTPUT);
    pinMode(M7_RIGHT, OUTPUT);
    pinMode(M7_STOP, OUTPUT);
    
    // Initialize button pins
    pinMode(SW1, INPUT_PULLUP);
    pinMode(SW2, INPUT_PULLUP);
    pinMode(SW3, INPUT_PULLUP);
    pinMode(SW4, INPUT_PULLUP);
    pinMode(SW5, INPUT_PULLUP);
    pinMode(SW6, INPUT_PULLUP);
    
    // Initialize beeper
    pinMode(BEEP, OUTPUT);
    
    // Initialize sensor pins
    for (int i = PD0; i <= PD13; i++) {
        pinMode(i, INPUT_PULLUP);
    }
    
    // Initialize encoder pins
    pinMode(ENC1_ENABLE, OUTPUT);
    pinMode(ENC2_ENABLE, OUTPUT);
    
    // Initial motor setup
    initializeMotors();
    
    // Startup beep sequence
    beep(250);
    delay(250);
    
    // Board self test display
    lcd.setCursor(0, 2);
    lcd.print("Board self testing  ");
    lcd.setCursor(0, 3);
    lcd.print("Please wait...      ");
    
    // Progress bar simulation
    for (int i = 0; i < 20; i++) {
        lcd.setCursor(i, 3);
        lcd.print("-");
        beep(50);
        delay(100);
    }
    
    // Ready screen
    lcd.setCursor(0, 2);
    lcd.print("TURBO Mode Ready!   ");
    lcd.setCursor(0, 3);
    lcd.print("Wait for Command    ");
    
    Serial.println("CORDON-82 v15.0a Ready!");
    Serial.println("Features: TURBO motors, M7 control, Demo scenario");
    Serial.println("Controls: SW1-SW6 buttons, SW1+SW6 for M7");
}

// ===== MAIN LOOP =====
void loop() {
    // Process button inputs
    processButtons();
    
    // Process UART commands
    processUART();
    
    // Process demo scenario
    processDemoScenario();
    
    // Small delay for main loop
    delay(10);
}

// ===== MOTOR FUNCTIONS =====
void initializeMotors() {
    // Disable all motors initially
    digitalWrite(M_ENABLE, HIGH);
    digitalWrite(DD16_ENABLE, LOW);
    
    // Stop M7
    stopM7();
    
    // Disable encoders
    digitalWrite(ENC1_ENABLE, LOW);
    digitalWrite(ENC2_ENABLE, LOW);
    
    Serial.println("Motors initialized - TURBO mode enabled");
}

void selectMotor(int motor) {
    // Select motor using binary selection pins
    digitalWrite(M_SEL1, (motor & 1) ? HIGH : LOW);
    digitalWrite(M_SEL2, (motor & 2) ? HIGH : LOW);
    digitalWrite(M_SEL3, (motor & 4) ? HIGH : LOW);
}

void enableMotorDriver() {
    digitalWrite(DD16_ENABLE, HIGH);
    delay(5);
    digitalWrite(M_ENABLE, LOW);
    delay(1);
}

void disableMotorDriver() {
    digitalWrite(M_ENABLE, HIGH);
    digitalWrite(DD16_ENABLE, LOW);
}

void motorStep(int motor, int direction, int steps) {
    if (motor < 1 || motor > 6) return;
    
    selectMotor(motor);
    enableMotorDriver();
    digitalWrite(M1_DIR, direction ? HIGH : LOW);
    
    for (int i = 0; i < steps; i++) {
        digitalWrite(M1_STEP, HIGH);
        
        if (motorSpeeds[motor].use_microseconds) {
            delayMicroseconds(motorSpeeds[motor].pulse_ms);
        } else {
            delay(motorSpeeds[motor].pulse_ms);
        }
        
        digitalWrite(M1_STEP, LOW);
        
        if (motorSpeeds[motor].use_microseconds) {
            delayMicroseconds(motorSpeeds[motor].pause_ms);
        } else {
            delay(motorSpeeds[motor].pause_ms);
        }
    }
    
    disableMotorDriver();
}

// ===== M7 CONTROL FUNCTIONS =====
void stopM7() {
    digitalWrite(M7_LEFT, LOW);
    digitalWrite(M7_RIGHT, LOW);
    digitalWrite(M7_STOP, HIGH);
}

void m7GoLeft() {
    digitalWrite(M7_LEFT, HIGH);
    digitalWrite(M7_RIGHT, LOW);
    digitalWrite(M7_STOP, LOW);
}

void m7GoRight() {
    digitalWrite(M7_LEFT, LOW);
    digitalWrite(M7_RIGHT, HIGH);
    digitalWrite(M7_STOP, LOW);
}

void handleM7Control() {
    switch(m7_state) {
        case 0: // Stop -> Left
            m7GoLeft();
            lcd.setCursor(0, 3);
            lcd.print("M7 Going LEFT       ");
            m7_state = 1;
            break;
            
        case 1: // Left -> Right
            m7GoRight();
            lcd.setCursor(0, 3);
            lcd.print("M7 Going RIGHT      ");
            m7_state = 2;
            break;
            
        case 2: // Right -> Stop
            stopM7();
            lcd.setCursor(0, 3);
            lcd.print("M7 STOPPED          ");
            m7_state = 0;
            break;
    }
    
    beep(200);
    
    // Wait for button release
    while (!digitalRead(SW1) || !digitalRead(SW6)) {
        delay(10);
    }
    delay(500); // Debounce
}

// ===== UTILITY FUNCTIONS =====
void beep(int duration) {
    digitalWrite(BEEP, HIGH);
    delay(duration);
    digitalWrite(BEEP, LOW);
}

void beepOK() {
    beep(100);
    delay(100);
    beep(100);
    delay(100);
    beep(200);
}

void beepError() {
    beep(250);
    delay(150);
    beep(100);
    delay(250);
    beep(250);
    delay(100);
    beep(250);
    delay(100);
    beep(250);
}
