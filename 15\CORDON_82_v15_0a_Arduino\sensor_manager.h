/*
 * CORDON-82 v15.0a Arduino Edition
 * МЕНЕДЖЕР ДАТЧИКОВ
 * 
 * Управление датчиками D1-D14 и энкодерами
 */

#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include <Arduino.h>
#include "config.h"

// ===== ИНИЦИАЛИЗАЦИЯ =====
void initializeSensors();

// ===== ЧТЕНИЕ ДАТЧИКОВ =====
bool readSensor(int sensor_number);
void readAllSensors(bool* sensor_states);

// ===== ЭНКОДЕРЫ =====
void enableEncoder(int encoder_number);
void disableEncoder(int encoder_number);
uint16_t readEncoder(int encoder_number);
void resetEncoder(int encoder_number);

// ===== ДИАГНОСТИКА =====
void printSensorStatus();
void testAllSensors();
bool checkSafetyConditions();

// ===== УТИЛИТЫ =====
bool waitForSensor(int sensor_number, uint16_t timeout_ms);
bool waitForSensorRelease(int sensor_number, uint16_t timeout_ms);

#endif // SENSOR_MANAGER_H
