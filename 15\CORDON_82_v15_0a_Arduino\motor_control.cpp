/*
 * CORDON-82 v15.0a Arduino Edition
 * РЕАЛИЗАЦИЯ УПРАВЛЕНИЯ МОТОРАМИ
 * 
 * Базовые функции для управления шаговыми моторами M1-M6 и DC мотором M7
 */

#include "motor_control.h"
#include "motor_macros.h"
#include "config.h"

// ===== ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ =====
static bool motors_initialized = false;
static uint32_t last_motor_operation = 0;

// ===== ИНИЦИАЛИЗАЦИЯ МОТОРОВ =====
void initializeMotors() {
    Serial.println("Initializing motor control system...");
    
    // Настройка пинов управления моторами
    pinMode(M1_STEP, OUTPUT);
    pinMode(M1_DIR, OUTPUT);
    pinMode(M_ENABLE, OUTPUT);
    pinMode(M_SEL1, OUTPUT);
    pinMode(M_SEL2, OUTPUT);
    pinMode(M_SEL3, OUTPUT);
    pinMode(DD16_ENABLE, OUTPUT);
    
    // Настройка пинов M7 (DC мотор)
    pinMode(M7_LEFT, OUTPUT);
    pinMode(M7_RIGHT, OUTPUT);
    pinMode(M7_STOP, OUTPUT);
    
    // Настройка пинов энкодеров
    pinMode(ENC1_ENABLE, OUTPUT);
    pinMode(ENC2_ENABLE, OUTPUT);
    
    // Начальное состояние - все моторы выключены
    disableMotorDriver();
    M7_STOP();
    
    // Выключить энкодеры
    digitalWrite(ENC1_ENABLE, LOW);
    digitalWrite(ENC2_ENABLE, LOW);
    
    // Установить начальное состояние селекторов
    digitalWrite(M_SEL1, LOW);
    digitalWrite(M_SEL2, LOW);
    digitalWrite(M_SEL3, LOW);
    
    motors_initialized = true;
    Serial.println("Motor control system initialized OK");
}

// ===== БАЗОВЫЕ ФУНКЦИИ УПРАВЛЕНИЯ =====

void selectMotor(int motor) {
    if (motor < 1 || motor > 6) {
        Serial.print("ERROR: Invalid motor number: ");
        Serial.println(motor);
        return;
    }
    
    // Выбор мотора через бинарные селекторы
    digitalWrite(M_SEL1, (motor & 1) ? HIGH : LOW);
    digitalWrite(M_SEL2, (motor & 2) ? HIGH : LOW);
    digitalWrite(M_SEL3, (motor & 4) ? HIGH : LOW);
    
    #if DEBUG_MOTORS
    Serial.print("Selected motor M");
    Serial.print(motor);
    Serial.print(" (SEL: ");
    Serial.print((motor & 4) ? 1 : 0);
    Serial.print((motor & 2) ? 1 : 0);
    Serial.print((motor & 1) ? 1 : 0);
    Serial.println(")");
    #endif
    
    // Небольшая задержка для стабилизации сигналов
    delayMicroseconds(10);
}

void enableMotorDriver() {
    // Включить DD16 драйвер
    digitalWrite(DD16_ENABLE, HIGH);
    delay(DELAY_MOTOR);
    
    // Включить основной драйвер (активный LOW)
    digitalWrite(M_ENABLE, LOW);
    delay(1);
    
    #if DEBUG_MOTORS
    Serial.println("Motor driver ENABLED");
    #endif
}

void disableMotorDriver() {
    // Выключить основной драйвер
    digitalWrite(M_ENABLE, HIGH);
    
    // Выключить DD16 драйвер
    digitalWrite(DD16_ENABLE, LOW);
    
    #if DEBUG_MOTORS
    Serial.println("Motor driver DISABLED");
    #endif
}

// ===== ОСНОВНАЯ ФУНКЦИЯ ДВИЖЕНИЯ МОТОРА =====
void motorStepRaw(int motor, int direction, int steps, int pulse_time, int pause_time, bool use_microseconds) {
    if (!motors_initialized) {
        Serial.println("ERROR: Motors not initialized!");
        return;
    }
    
    if (motor < 1 || motor > 6) {
        Serial.print("ERROR: Invalid motor number: ");
        Serial.println(motor);
        return;
    }
    
    if (steps <= 0) {
        Serial.println("WARNING: Zero or negative steps requested");
        return;
    }
    
    #if DEBUG_MOTORS
    Serial.print("Motor M");
    Serial.print(motor);
    Serial.print(" moving ");
    Serial.print(steps);
    Serial.print(" steps ");
    Serial.print(direction ? "FORWARD" : "BACK");
    Serial.print(" (");
    Serial.print(pulse_time);
    Serial.print(use_microseconds ? "μS+" : "ms+");
    Serial.print(pause_time);
    Serial.println(use_microseconds ? "μS)" : "ms)");
    #endif
    
    // Выбрать мотор
    selectMotor(motor);
    
    // Включить драйвер
    enableMotorDriver();
    
    // Установить направление
    digitalWrite(M1_DIR, direction ? HIGH : LOW);
    
    // Небольшая задержка для стабилизации
    delayMicroseconds(50);
    
    // Выполнить шаги
    for (int i = 0; i < steps; i++) {
        // Импульс HIGH
        digitalWrite(M1_STEP, HIGH);
        
        if (use_microseconds) {
            delayMicroseconds(pulse_time);
        } else {
            delay(pulse_time);
        }
        
        // Импульс LOW
        digitalWrite(M1_STEP, LOW);
        
        if (use_microseconds) {
            delayMicroseconds(pause_time);
        } else {
            delay(pause_time);
        }
        
        // Проверка на аварийную остановку каждые 10 шагов
        if (i % 10 == 0) {
            // Здесь можно добавить проверку аварийных условий
            // if (emergency_stop_requested) break;
        }
    }
    
    // Выключить драйвер
    disableMotorDriver();
    
    // Обновить время последней операции
    last_motor_operation = millis();
    
    #if DEBUG_MOTORS
    Serial.print("Motor M");
    Serial.print(motor);
    Serial.println(" movement completed");
    #endif
}

// ===== ФУНКЦИИ M7 (DC МОТОР) =====

void m7Stop() {
    digitalWrite(M7_LEFT, LOW);
    digitalWrite(M7_RIGHT, LOW);
    digitalWrite(M7_STOP, HIGH);
    
    #if DEBUG_MOTORS
    Serial.println("M7 STOPPED");
    #endif
}

void m7GoLeft() {
    digitalWrite(M7_LEFT, HIGH);
    digitalWrite(M7_RIGHT, LOW);
    digitalWrite(M7_STOP, LOW);
    
    #if DEBUG_MOTORS
    Serial.println("M7 Going LEFT");
    #endif
}

void m7GoRight() {
    digitalWrite(M7_LEFT, LOW);
    digitalWrite(M7_RIGHT, HIGH);
    digitalWrite(M7_STOP, LOW);
    
    #if DEBUG_MOTORS
    Serial.println("M7 Going RIGHT");
    #endif
}

// ===== ВЫСОКОУРОВНЕВЫЕ ФУНКЦИИ =====

void testMotor(int motor) {
    if (motor < 1 || motor > 7) {
        Serial.print("ERROR: Invalid motor for test: ");
        Serial.println(motor);
        return;
    }
    
    Serial.print("Testing motor M");
    Serial.println(motor);
    
    switch(motor) {
        case 1:
            TEST_M1();
            break;
        case 2:
            TEST_M2();
            break;
        case 3:
            TEST_M3();
            break;
        case 4:
            TEST_M4();
            break;
        case 5:
            TEST_M5();
            break;
        case 6:
            TEST_M6();
            break;
        case 7:
            // M7 тест - кратковременное движение
            m7GoLeft();
            delay(500);
            m7Stop();
            delay(250);
            m7GoRight();
            delay(500);
            m7Stop();
            break;
    }
    
    Serial.print("Motor M");
    Serial.print(motor);
    Serial.println(" test completed");
}

void emergencyStopAll() {
    Serial.println("EMERGENCY STOP ALL MOTORS!");
    
    EMERGENCY_STOP_ALL();
    
    Serial.println("All motors stopped");
}

void softStopAll() {
    Serial.println("Soft stop all motors");
    
    SOFT_STOP_ALL();
    
    Serial.println("All motors soft stopped");
}

// ===== ДИАГНОСТИЧЕСКИЕ ФУНКЦИИ =====

bool isMotorSystemReady() {
    return motors_initialized;
}

uint32_t getLastMotorOperationTime() {
    return last_motor_operation;
}

void printMotorStatus() {
    Serial.println("=== MOTOR SYSTEM STATUS ===");
    Serial.print("Initialized: ");
    Serial.println(motors_initialized ? "YES" : "NO");
    Serial.print("Last operation: ");
    Serial.print(millis() - last_motor_operation);
    Serial.println(" ms ago");
    
    Serial.print("Driver enable: ");
    Serial.println(digitalRead(M_ENABLE) ? "DISABLED" : "ENABLED");
    Serial.print("DD16 enable: ");
    Serial.println(digitalRead(DD16_ENABLE) ? "ENABLED" : "DISABLED");
    
    Serial.print("M7 state: ");
    if (digitalRead(M7_STOP)) {
        Serial.println("STOPPED");
    } else if (digitalRead(M7_LEFT)) {
        Serial.println("LEFT");
    } else if (digitalRead(M7_RIGHT)) {
        Serial.println("RIGHT");
    } else {
        Serial.println("UNKNOWN");
    }
    
    Serial.print("Motor selection: ");
    int sel = (digitalRead(M_SEL3) << 2) | (digitalRead(M_SEL2) << 1) | digitalRead(M_SEL1);
    Serial.println(sel);
    
    Serial.println("=========================");
}
