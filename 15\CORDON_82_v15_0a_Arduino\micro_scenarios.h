/*
 * CORDON-82 v15.0a Arduino Edition
 * УРОВЕНЬ 2: МИКРОСЦЕНАРИИ
 * 
 * Логические блоки движений, составленные из макросов
 * Микросценарий = логически завершенное действие (например, движение от D1 до D2)
 */

#ifndef MICRO_SCENARIOS_H
#define MICRO_SCENARIOS_H

#include "motor_macros.h"
#include "sensor_manager.h"
#include "config.h"

// ===== МИКРОСЦЕНАРИИ M1 (АЗИМУТ) =====

// Движение M1 от датчика D14 до D13 максимальной скоростью
void M1_MOVE_D14_TO_D13_FAST();

// Движение M1 от текущей позиции до D14 (исходное положение)
void M1_MOVE_TO_HOME_POSITION();

// Поворот M1 на заданный угол с максимальной скоростью
void M1_ROTATE_ANGLE_FAST(uint16_t angle_degrees);

// Точное позиционирование M1 с использованием энкодера
void M1_PRECISE_POSITIONING(uint16_t target_position);

// Калибровка M1 - поиск нулевой позиции
void M1_CALIBRATE_ZERO_POSITION();

// ===== МИКРОСЦЕНАРИИ M2 (УГОЛ ВОЗВЫШЕНИЯ) =====

// Движение M2 от датчика D13 до D12 максимальной скоростью
void M2_MOVE_D13_TO_D12_FAST();

// Движение M2 от текущей позиции до D13 (исходное положение)
void M2_MOVE_TO_HOME_POSITION();

// Установка M2 на заданный угол возвышения
void M2_SET_ELEVATION_ANGLE(uint16_t elevation_degrees);

// Точное позиционирование M2 с использованием энкодера
void M2_PRECISE_POSITIONING(uint16_t target_position);

// Калибровка M2 - поиск нулевой позиции
void M2_CALIBRATE_ZERO_POSITION();

// ===== МИКРОСЦЕНАРИИ M3 (ПОДАЧА СНАРЯДА) =====

// Цикл подачи снаряда - движение от D1 до D2 с контролем датчиков
void M3_SHELL_FEED_CYCLE();

// Возврат M3 в исходное положение (к датчику D1)
void M3_RETURN_TO_D1();

// Движение M3 вперед до срабатывания датчика D2
void M3_MOVE_FORWARD_TO_D2();

// Движение M3 назад до срабатывания датчика D1
void M3_MOVE_BACK_TO_D1();

// Точная подача снаряда с контролем скорости
void M3_PRECISE_SHELL_FEED();

// ===== МИКРОСЦЕНАРИИ M4 (ДОСЫЛКА) =====

// Полный цикл досылки - движение от D9 до D8 и обратно
void M4_FULL_RAMMER_CYCLE();

// Движение M4 вперед до датчика D8 (досылка снаряда)
void M4_RAMMER_FORWARD_TO_D8();

// Движение M4 назад до датчика D9 (возврат досылателя)
void M4_RAMMER_BACK_TO_D9();

// Быстрая досылка с максимальной скоростью
void M4_FAST_RAMMER_CYCLE();

// Медленная точная досылка
void M4_PRECISE_RAMMER_CYCLE();

// ===== МИКРОСЦЕНАРИИ M5 (ФИКСАЦИЯ) =====

// Цикл фиксации снаряда - движение от D5 до D6 и обратно
void M5_SHELL_LOCK_CYCLE();

// Фиксация снаряда - движение M5 до датчика D6
void M5_LOCK_SHELL_TO_D6();

// Освобождение снаряда - движение M5 до датчика D5
void M5_RELEASE_SHELL_TO_D5();

// Быстрая фиксация с максимальной скоростью
void M5_FAST_LOCK_CYCLE();

// Точная фиксация с контролем усилия
void M5_PRECISE_LOCK_CYCLE();

// ===== МИКРОСЦЕНАРИИ M6 (БАРАБАН МАГАЗИНА) =====

// Поворот барабана на одну позицию (подача следующего снаряда)
void M6_ROTATE_ONE_POSITION();

// Поворот барабана на заданное количество позиций
void M6_ROTATE_POSITIONS(uint8_t positions);

// Поиск снаряда в барабане (поворот до срабатывания D3)
void M6_FIND_NEXT_SHELL();

// Полный оборот барабана для подсчета снарядов
void M6_FULL_ROTATION_COUNT();

// Установка барабана в исходное положение (датчик D4)
void M6_MOVE_TO_HOME_POSITION();

// Мощный поворот барабана (для преодоления заедания)
void M6_POWER_ROTATION(uint8_t positions);

// ===== МИКРОСЦЕНАРИИ M7 (ДОСЫЛАТЕЛЬ DC) =====

// Полный цикл досылателя с контролем тока
void M7_FULL_RAMMER_CYCLE_WITH_CURRENT_CONTROL();

// Движение M7 влево до срабатывания датчика D11
void M7_MOVE_LEFT_TO_D11();

// Движение M7 вправо до срабатывания датчика D10
void M7_MOVE_RIGHT_TO_D10();

// Возврат M7 в центральное положение
void M7_RETURN_TO_CENTER();

// Контролируемое движение M7 с таймаутом
void M7_CONTROLLED_MOVEMENT(uint8_t direction, uint16_t timeout_ms);

// ===== КОМБИНИРОВАННЫЕ МИКРОСЦЕНАРИИ =====

// Полная подготовка к стрельбе (M1+M2 в исходные позиции)
void PREPARE_FOR_FIRING();

// Полный цикл заряжания (M3+M4+M5 последовательно)
void FULL_LOADING_CYCLE();

// Быстрое наведение на цель (M1+M2 одновременно)
void FAST_TARGET_ACQUISITION(uint16_t azimuth, uint16_t elevation);

// Аварийное приведение всех моторов в безопасное положение
void EMERGENCY_SAFE_POSITION();

// Калибровка всех моторов (поиск исходных позиций)
void CALIBRATE_ALL_MOTORS();

// ===== ДИАГНОСТИЧЕСКИЕ МИКРОСЦЕНАРИИ =====

// Тест всех моторов по очереди
void TEST_ALL_MOTORS_SEQUENCE();

// Проверка всех датчиков
void TEST_ALL_SENSORS();

// Проверка связи моторов с датчиками
void TEST_MOTOR_SENSOR_CORRELATION();

// Измерение времени выполнения операций
void BENCHMARK_MOTOR_SPEEDS();

// ===== СЕРВИСНЫЕ МИКРОСЦЕНАРИИ =====

// Смазка механизмов (медленные движения всех моторов)
void LUBRICATION_CYCLE();

// Прогрев моторов (короткие движения для разогрева)
void MOTOR_WARMUP_CYCLE();

// Проверка механических ограничений
void CHECK_MECHANICAL_LIMITS();

// Очистка механизмов (специальные движения для удаления грязи)
void CLEANING_CYCLE();

#endif // MICRO_SCENARIOS_H
