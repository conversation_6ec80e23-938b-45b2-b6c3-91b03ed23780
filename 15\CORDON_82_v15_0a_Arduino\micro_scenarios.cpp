/*
 * CORDON-82 v15.0a Arduino Edition
 * РЕАЛИЗАЦИЯ МИКРОСЦЕНАРИЕВ
 * 
 * Логические блоки движений, составленные из макросов
 */

#include "micro_scenarios.h"
#include "motor_macros.h"
#include "sensor_manager.h"
#include "config.h"

// ===== МИКРОСЦЕНАРИИ M1 (АЗИМУТ) =====

void M1_MOVE_D14_TO_D13_FAST() {
    Serial.println("M1: Moving from D14 to D13 (FAST)");
    
    // Проверить, что мы находимся у D14
    if (!readSensor(14)) {
        Serial.println("WARNING: M1 not at D14 position");
    }
    
    // Движение с максимальной скоростью до срабатывания D13
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 1000; // Максимальное количество шагов для безопасности
    
    while (!readSensor(13) && steps < MAX_STEPS) {
        FAST_M1_CW(1); // По одному шагу с проверкой
        steps++;
        delay(1); // Небольшая задержка для стабилизации датчика
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M1 failed to reach D13 - timeout");
    } else {
        Serial.print("M1: Reached D13 in ");
        Serial.print(steps);
        Serial.println(" steps");
    }
}

void M1_MOVE_TO_HOME_POSITION() {
    Serial.println("M1: Moving to home position (D14)");
    
    // Если уже дома - ничего не делать
    if (readSensor(14)) {
        Serial.println("M1: Already at home position");
        return;
    }
    
    // Поиск домашней позиции (предполагаем, что D14 находится по часовой стрелке)
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 2000; // Максимальное количество шагов
    
    while (!readSensor(14) && steps < MAX_STEPS) {
        FAST_M1_CW(1);
        steps++;
        delay(1);
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M1 failed to find home position");
    } else {
        Serial.println("M1: Home position reached");
    }
}

void M1_ROTATE_ANGLE_FAST(uint16_t angle_degrees) {
    Serial.print("M1: Rotating ");
    Serial.print(angle_degrees);
    Serial.println(" degrees (FAST)");
    
    // Примерный расчет: 1 градус = 10 шагов (нужно калибровать)
    uint16_t steps = angle_degrees * 10;
    
    if (angle_degrees > 0) {
        FAST_M1_CW(steps);
    } else {
        FAST_M1_CCW(steps);
    }
    
    Serial.println("M1: Angle rotation completed");
}

// ===== МИКРОСЦЕНАРИИ M2 (УГОЛ ВОЗВЫШЕНИЯ) =====

void M2_MOVE_TO_HOME_POSITION() {
    Serial.println("M2: Moving to home position (D13)");
    
    if (readSensor(13)) {
        Serial.println("M2: Already at home position");
        return;
    }
    
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 1500;
    
    while (!readSensor(13) && steps < MAX_STEPS) {
        FAST_M2_DOWN(1);
        steps++;
        delay(2); // M2 медленнее, больше задержка
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M2 failed to find home position");
    } else {
        Serial.println("M2: Home position reached");
    }
}

void M2_SET_ELEVATION_ANGLE(uint16_t elevation_degrees) {
    Serial.print("M2: Setting elevation to ");
    Serial.print(elevation_degrees);
    Serial.println(" degrees");
    
    // Сначала идем в домашнюю позицию
    M2_MOVE_TO_HOME_POSITION();
    
    // Затем поднимаем на нужный угол
    // Примерный расчет: 1 градус = 8 шагов (нужно калибровать)
    uint16_t steps = elevation_degrees * 8;
    
    if (steps > 0) {
        FAST_M2_UP(steps);
    }
    
    Serial.println("M2: Elevation angle set");
}

// ===== МИКРОСЦЕНАРИИ M3 (ПОДАЧА СНАРЯДА) =====

void M3_SHELL_FEED_CYCLE() {
    Serial.println("M3: Starting shell feed cycle");
    
    // Убедиться, что находимся в исходной позиции D1
    M3_RETURN_TO_D1();
    
    // Движение вперед до D2 (подача снаряда)
    M3_MOVE_FORWARD_TO_D2();
    
    // Небольшая пауза для стабилизации
    delay(100);
    
    // Возврат в исходную позицию
    M3_RETURN_TO_D1();
    
    Serial.println("M3: Shell feed cycle completed");
}

void M3_MOVE_FORWARD_TO_D2() {
    Serial.println("M3: Moving forward to D2");
    
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 500;
    
    while (!readSensor(2) && steps < MAX_STEPS) {
        FAST_M3_FWD(1);
        steps++;
        delay(1);
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M3 failed to reach D2");
    } else {
        Serial.println("M3: Reached D2");
    }
}

void M3_RETURN_TO_D1() {
    Serial.println("M3: Returning to D1");
    
    if (readSensor(1)) {
        Serial.println("M3: Already at D1");
        return;
    }
    
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 500;
    
    while (!readSensor(1) && steps < MAX_STEPS) {
        FAST_M3_BACK(1);
        steps++;
        delay(1);
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M3 failed to reach D1");
    } else {
        Serial.println("M3: Reached D1");
    }
}

// ===== МИКРОСЦЕНАРИИ M4 (ДОСЫЛКА) =====

void M4_FULL_RAMMER_CYCLE() {
    Serial.println("M4: Starting full rammer cycle");
    
    // Убедиться, что находимся в исходной позиции D9
    M4_RAMMER_BACK_TO_D9();
    
    // Досылка вперед до D8
    M4_RAMMER_FORWARD_TO_D8();
    
    // Пауза для досылки
    delay(200);
    
    // Возврат назад до D9
    M4_RAMMER_BACK_TO_D9();
    
    Serial.println("M4: Rammer cycle completed");
}

void M4_RAMMER_FORWARD_TO_D8() {
    Serial.println("M4: Rammer forward to D8");
    
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 800; // M4 может делать много шагов
    
    while (!readSensor(8) && steps < MAX_STEPS) {
        FAST_M4_FWD(1);
        steps++;
        delayMicroseconds(100); // M4 работает в микросекундах
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M4 failed to reach D8");
    } else {
        Serial.println("M4: Reached D8");
    }
}

void M4_RAMMER_BACK_TO_D9() {
    Serial.println("M4: Rammer back to D9");
    
    if (readSensor(9)) {
        Serial.println("M4: Already at D9");
        return;
    }
    
    uint16_t steps = 0;
    const uint16_t MAX_STEPS = 800;
    
    while (!readSensor(9) && steps < MAX_STEPS) {
        FAST_M4_BACK(1);
        steps++;
        delayMicroseconds(100);
    }
    
    if (steps >= MAX_STEPS) {
        Serial.println("ERROR: M4 failed to reach D9");
    } else {
        Serial.println("M4: Reached D9");
    }
}

// ===== МИКРОСЦЕНАРИИ M6 (БАРАБАН МАГАЗИНА) =====

void M6_ROTATE_ONE_POSITION() {
    Serial.println("M6: Rotating one position");
    
    // Поворот барабана на одну позицию (примерно 60 градусов для 6-зарядного барабана)
    // Используем мощный режим для преодоления возможного заедания
    POWER_M6_FWD(30); // Примерно 30 шагов на позицию (нужно калибровать)
    
    Serial.println("M6: One position rotation completed");
}

void M6_FIND_NEXT_SHELL() {
    Serial.println("M6: Finding next shell");
    
    uint8_t positions_checked = 0;
    const uint8_t MAX_POSITIONS = 8; // Максимум 8 позиций для безопасности
    
    while (!readSensor(3) && positions_checked < MAX_POSITIONS) {
        M6_ROTATE_ONE_POSITION();
        positions_checked++;
        delay(100); // Пауза для стабилизации
    }
    
    if (positions_checked >= MAX_POSITIONS) {
        Serial.println("ERROR: No shell found in magazine");
    } else {
        Serial.print("M6: Shell found after ");
        Serial.print(positions_checked);
        Serial.println(" positions");
    }
}

// ===== КОМБИНИРОВАННЫЕ МИКРОСЦЕНАРИИ =====

void PREPARE_FOR_FIRING() {
    Serial.println("=== PREPARING FOR FIRING ===");
    
    // Привести M1 и M2 в исходные позиции
    M1_MOVE_TO_HOME_POSITION();
    M2_MOVE_TO_HOME_POSITION();
    
    // Проверить, что все в порядке
    if (readSensor(14) && readSensor(13)) {
        Serial.println("PREPARE_FOR_FIRING: SUCCESS");
    } else {
        Serial.println("PREPARE_FOR_FIRING: FAILED");
    }
}

void FULL_LOADING_CYCLE() {
    Serial.println("=== FULL LOADING CYCLE ===");
    
    // 1. Подача снаряда (M3)
    M3_SHELL_FEED_CYCLE();
    
    // 2. Досылка снаряда (M4)
    M4_FULL_RAMMER_CYCLE();
    
    // 3. Фиксация снаряда (M5) - пока заглушка
    Serial.println("M5: Shell locking (placeholder)");
    FAST_M5_FWD(50);
    delay(200);
    FAST_M5_BACK(50);
    
    Serial.println("FULL_LOADING_CYCLE: COMPLETED");
}

void FAST_TARGET_ACQUISITION(uint16_t azimuth, uint16_t elevation) {
    Serial.println("=== FAST TARGET ACQUISITION ===");
    Serial.print("Target: Az=");
    Serial.print(azimuth);
    Serial.print("°, El=");
    Serial.print(elevation);
    Serial.println("°");
    
    // Сначала привести в исходные позиции
    PREPARE_FOR_FIRING();
    
    // Затем навести на цель
    M1_ROTATE_ANGLE_FAST(azimuth);
    M2_SET_ELEVATION_ANGLE(elevation);
    
    Serial.println("FAST_TARGET_ACQUISITION: COMPLETED");
}

void TEST_ALL_MOTORS_SEQUENCE() {
    Serial.println("=== TESTING ALL MOTORS ===");
    
    for (int motor = 1; motor <= 7; motor++) {
        Serial.print("Testing motor M");
        Serial.println(motor);
        
        testMotor(motor);
        
        delay(1000); // Пауза между тестами
    }
    
    Serial.println("ALL MOTORS TEST: COMPLETED");
}
