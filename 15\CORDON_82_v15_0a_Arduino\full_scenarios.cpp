/*
 * CORDON-82 v15.0a Arduino Edition
 * РЕАЛИЗАЦИЯ ПОЛНЫХ СЦЕНАРИЕВ
 * 
 * Комплексные операции, составленные из микросценариев и макросов
 */

#include "full_scenarios.h"
#include "micro_scenarios.h"
#include "motor_macros.h"
#include "sensor_manager.h"
#include "config.h"

// ===== ОСНОВНЫЕ БОЕВЫЕ СЦЕНАРИИ =====

void FULL_FIRE_CYCLE(uint16_t azimuth_degrees, uint16_t elevation_degrees) {
    Serial.println("========================================");
    Serial.println("STARTING FULL FIRE CYCLE");
    Serial.print("Target: Az=");
    Serial.print(azimuth_degrees);
    Serial.print("°, El=");
    Serial.print(elevation_degrees);
    Serial.println("°");
    Serial.println("========================================");
    
    logScenarioStep("Fire cycle initiated");
    
    // Проверка готовности системы
    if (!checkScenarioReadiness()) {
        Serial.println("ERROR: System not ready for fire cycle");
        reportScenarioStatus("FULL_FIRE_CYCLE", false);
        return;
    }
    
    // Этап 1: Подготовка к стрельбе
    logScenarioStep("Stage 1: Preparation");
    PREPARE_FOR_FIRING();
    delay(500);
    
    // Этап 2: Поиск снаряда в магазине
    logScenarioStep("Stage 2: Finding shell in magazine");
    M6_FIND_NEXT_SHELL();
    delay(300);
    
    // Этап 3: Наведение на цель
    logScenarioStep("Stage 3: Target acquisition");
    FAST_TARGET_ACQUISITION(azimuth_degrees, elevation_degrees);
    delay(500);
    
    // Этап 4: Полный цикл заряжания
    logScenarioStep("Stage 4: Loading sequence");
    FULL_LOADING_CYCLE();
    delay(300);
    
    // Этап 5: Финальная проверка
    logScenarioStep("Stage 5: Final checks");
    if (!checkSafetyConditions()) {
        Serial.println("ERROR: Safety conditions not met");
        EMERGENCY_SAFE_POSITION();
        reportScenarioStatus("FULL_FIRE_CYCLE", false);
        return;
    }
    
    // Этап 6: Имитация выстрела (в демо-версии)
    logScenarioStep("Stage 6: Fire simulation");
    Serial.println("FIRE! (simulation)");
    for (int i = 0; i < 5; i++) {
        digitalWrite(BEEP, HIGH);
        delay(100);
        digitalWrite(BEEP, LOW);
        delay(100);
    }
    
    // Этап 7: Возврат в исходное положение
    logScenarioStep("Stage 7: Return to home position");
    RETURN_TO_HOME_POSITION();
    
    Serial.println("========================================");
    Serial.println("FULL FIRE CYCLE COMPLETED SUCCESSFULLY");
    Serial.println("========================================");
    
    reportScenarioStatus("FULL_FIRE_CYCLE", true);
}

void RAPID_FIRE_SEQUENCE(uint16_t azimuth, uint16_t elevation) {
    Serial.println("=== RAPID FIRE SEQUENCE ===");
    
    logScenarioStep("Rapid fire initiated");
    
    // Быстрое наведение
    FAST_TARGET_ACQUISITION(azimuth, elevation);
    
    // Быстрое заряжание
    RAPID_LOADING_SEQUENCE();
    
    // Имитация быстрого выстрела
    Serial.println("RAPID FIRE!");
    digitalWrite(BEEP, HIGH);
    delay(200);
    digitalWrite(BEEP, LOW);
    
    reportScenarioStatus("RAPID_FIRE_SEQUENCE", true);
}

void BURST_FIRE_SEQUENCE(uint16_t azimuth, uint16_t elevation, uint8_t rounds) {
    Serial.println("=== BURST FIRE SEQUENCE ===");
    Serial.print("Firing ");
    Serial.print(rounds);
    Serial.println(" rounds");
    
    logScenarioStep("Burst fire initiated");
    
    // Наведение на цель
    FAST_TARGET_ACQUISITION(azimuth, elevation);
    
    // Серийная стрельба
    for (uint8_t round = 1; round <= rounds; round++) {
        Serial.print("Round ");
        Serial.print(round);
        Serial.print(" of ");
        Serial.println(rounds);
        
        // Заряжание
        RAPID_LOADING_SEQUENCE();
        
        // Выстрел
        Serial.println("FIRE!");
        digitalWrite(BEEP, HIGH);
        delay(150);
        digitalWrite(BEEP, LOW);
        delay(500); // Пауза между выстрелами
        
        // Поворот барабана для следующего снаряда
        if (round < rounds) {
            M6_ROTATE_ONE_POSITION();
        }
    }
    
    reportScenarioStatus("BURST_FIRE_SEQUENCE", true);
}

// ===== СЦЕНАРИИ ПОДГОТОВКИ =====

void SYSTEM_STARTUP_SEQUENCE() {
    Serial.println("=== SYSTEM STARTUP SEQUENCE ===");
    
    logScenarioStep("System startup initiated");
    
    // Инициализация всех систем
    initializeSensors();
    initializeMotors();
    
    // Проверка всех датчиков
    TEST_ALL_SENSORS();
    
    // Калибровка моторов
    CALIBRATE_ALL_MOTORS();
    
    // Тест всех механизмов
    TEST_ALL_MOTORS_SEQUENCE();
    
    Serial.println("SYSTEM STARTUP COMPLETED");
    reportScenarioStatus("SYSTEM_STARTUP_SEQUENCE", true);
}

void COMBAT_READY_SEQUENCE() {
    Serial.println("=== COMBAT READY SEQUENCE ===");
    
    logScenarioStep("Combat preparation initiated");
    
    // Приведение в боевое положение
    PREPARE_FOR_FIRING();
    
    // Проверка магазина
    M6_FIND_NEXT_SHELL();
    
    // Проверка всех систем
    if (checkSafetyConditions()) {
        Serial.println("SYSTEM COMBAT READY");
        // Сигнал готовности
        for (int i = 0; i < 3; i++) {
            digitalWrite(BEEP, HIGH);
            delay(200);
            digitalWrite(BEEP, LOW);
            delay(200);
        }
    } else {
        Serial.println("SYSTEM NOT READY FOR COMBAT");
        EMERGENCY_SAFE_POSITION();
    }
    
    reportScenarioStatus("COMBAT_READY_SEQUENCE", true);
}

// ===== СЦЕНАРИИ ЗАРЯЖАНИЯ =====

void COMPLETE_LOADING_SEQUENCE() {
    Serial.println("=== COMPLETE LOADING SEQUENCE ===");
    
    logScenarioStep("Complete loading initiated");
    
    // Полный цикл заряжания с проверками
    FULL_LOADING_CYCLE();
    
    // Дополнительные проверки
    if (readSensor(2) && readSensor(8)) {
        Serial.println("LOADING COMPLETED - Shell in position");
    } else {
        Serial.println("LOADING FAILED - Shell not detected");
    }
    
    reportScenarioStatus("COMPLETE_LOADING_SEQUENCE", true);
}

void RAPID_LOADING_SEQUENCE() {
    Serial.println("=== RAPID LOADING SEQUENCE ===");
    
    logScenarioStep("Rapid loading initiated");
    
    // Быстрая подача снаряда
    FAST_M3_FWD(100);
    delay(100);
    
    // Быстрая досылка
    FAST_M4_FWD(200);
    delay(100);
    FAST_M4_BACK(200);
    
    // Быстрая фиксация
    FAST_M5_FWD(50);
    delay(100);
    
    Serial.println("RAPID LOADING COMPLETED");
    reportScenarioStatus("RAPID_LOADING_SEQUENCE", true);
}

// ===== СЦЕНАРИИ НАВЕДЕНИЯ =====

void PRECISION_TARGETING(uint16_t azimuth, uint16_t elevation) {
    Serial.println("=== PRECISION TARGETING ===");
    Serial.print("Precise targeting: Az=");
    Serial.print(azimuth);
    Serial.print("°, El=");
    Serial.print(elevation);
    Serial.println("°");
    
    logScenarioStep("Precision targeting initiated");
    
    // Точное наведение с медленными скоростями
    PREPARE_FOR_FIRING();
    
    // Точное позиционирование азимута
    M1_ROTATE_ANGLE_FAST(azimuth);
    delay(200);
    PRECISE_M1_CW(5); // Точная подстройка
    
    // Точное позиционирование угла возвышения
    M2_SET_ELEVATION_ANGLE(elevation);
    delay(200);
    PRECISE_M2_UP(3); // Точная подстройка
    
    Serial.println("PRECISION TARGETING COMPLETED");
    reportScenarioStatus("PRECISION_TARGETING", true);
}

void RETURN_TO_HOME_POSITION() {
    Serial.println("=== RETURN TO HOME POSITION ===");
    
    logScenarioStep("Returning to home position");
    
    // Возврат всех моторов в исходные позиции
    M1_MOVE_TO_HOME_POSITION();
    M2_MOVE_TO_HOME_POSITION();
    M3_RETURN_TO_D1();
    M4_RAMMER_BACK_TO_D9();
    M6_MOVE_TO_HOME_POSITION();
    
    // Остановка M7
    M7_STOP();
    
    Serial.println("HOME POSITION REACHED");
    reportScenarioStatus("RETURN_TO_HOME_POSITION", true);
}

// ===== АВАРИЙНЫЕ СЦЕНАРИИ =====

void EMERGENCY_SHUTDOWN() {
    Serial.println("!!! EMERGENCY SHUTDOWN !!!");
    
    logScenarioStep("EMERGENCY SHUTDOWN ACTIVATED");
    
    // Немедленная остановка всех моторов
    EMERGENCY_STOP_ALL();
    
    // Аварийные сигналы
    for (int i = 0; i < 10; i++) {
        digitalWrite(BEEP, HIGH);
        delay(100);
        digitalWrite(BEEP, LOW);
        delay(100);
    }
    
    Serial.println("ALL SYSTEMS STOPPED");
    reportScenarioStatus("EMERGENCY_SHUTDOWN", true);
}

void EMERGENCY_SAFE_POSITION() {
    Serial.println("!!! EMERGENCY SAFE POSITION !!!");
    
    logScenarioStep("Emergency safe position activated");
    
    // Остановка всех моторов
    SOFT_STOP_ALL();
    
    // Приведение в безопасное положение
    RETURN_TO_HOME_POSITION();
    
    Serial.println("EMERGENCY SAFE POSITION REACHED");
    reportScenarioStatus("EMERGENCY_SAFE_POSITION", true);
}

// ===== ДЕМОНСТРАЦИОННЫЕ СЦЕНАРИИ =====

void FULL_DEMONSTRATION() {
    Serial.println("========================================");
    Serial.println("FULL SYSTEM DEMONSTRATION");
    Serial.println("========================================");
    
    logScenarioStep("Full demonstration started");
    
    // Демонстрация всех возможностей
    Serial.println("1. System startup...");
    SYSTEM_STARTUP_SEQUENCE();
    delay(1000);
    
    Serial.println("2. Speed demonstration...");
    SPEED_DEMONSTRATION();
    delay(1000);
    
    Serial.println("3. Precision demonstration...");
    PRECISION_DEMONSTRATION();
    delay(1000);
    
    Serial.println("4. Fire cycle demonstration...");
    FULL_FIRE_CYCLE(90, 30);
    delay(1000);
    
    Serial.println("5. Return to home...");
    RETURN_TO_HOME_POSITION();
    
    Serial.println("========================================");
    Serial.println("FULL DEMONSTRATION COMPLETED");
    Serial.println("========================================");
    
    reportScenarioStatus("FULL_DEMONSTRATION", true);
}

void SPEED_DEMONSTRATION() {
    Serial.println("=== SPEED DEMONSTRATION ===");
    
    logScenarioStep("Speed demonstration started");
    
    Serial.println("TURBO mode demonstration:");
    TEST_M1();
    delay(500);
    TEST_M2();
    delay(500);
    TEST_M3();
    delay(500);
    
    Serial.println("POWER mode demonstration:");
    TEST_M6();
    delay(500);
    
    Serial.println("PRECISION mode demonstration:");
    PRECISE_M1_CW(20);
    delay(500);
    PRECISE_M2_UP(15);
    delay(500);
    
    reportScenarioStatus("SPEED_DEMONSTRATION", true);
}

// ===== УТИЛИТАРНЫЕ ФУНКЦИИ =====

bool checkScenarioReadiness() {
    Serial.println("Checking scenario readiness...");
    
    // Проверка инициализации моторов
    if (!isMotorSystemReady()) {
        Serial.println("ERROR: Motor system not ready");
        return false;
    }
    
    // Проверка безопасности
    if (!checkSafetyConditions()) {
        Serial.println("ERROR: Safety conditions not met");
        return false;
    }
    
    Serial.println("System ready for scenario execution");
    return true;
}

void reportScenarioStatus(const char* scenario_name, bool success) {
    Serial.print("SCENARIO: ");
    Serial.print(scenario_name);
    Serial.print(" - ");
    Serial.println(success ? "SUCCESS" : "FAILED");
    
    if (success) {
        // Сигнал успеха
        digitalWrite(BEEP, HIGH);
        delay(100);
        digitalWrite(BEEP, LOW);
        delay(100);
        digitalWrite(BEEP, HIGH);
        delay(200);
        digitalWrite(BEEP, LOW);
    } else {
        // Сигнал ошибки
        for (int i = 0; i < 3; i++) {
            digitalWrite(BEEP, HIGH);
            delay(250);
            digitalWrite(BEEP, LOW);
            delay(150);
        }
    }
}

void logScenarioStep(const char* step_description) {
    Serial.print("[SCENARIO] ");
    Serial.println(step_description);
}
