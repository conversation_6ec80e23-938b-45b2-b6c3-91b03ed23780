/*
 * CORDON-82 v15.0a Arduino Edition
 * РЕАЛИЗАЦИЯ МЕНЕДЖЕРА ДАТЧИКОВ
 */

#include "sensor_manager.h"
#include "config.h"

// ===== ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ =====
static bool sensors_initialized = false;
static bool sensor_states[15]; // D1-D14 (индекс 0 не используется)

// Массив пинов датчиков для удобства
static const int sensor_pins[15] = {
    0,   // Индекс 0 не используется
    D1, D2, D3, D4, D5, D6, D7, D8, D9, D10, D11, D12, D13, D14
};

// ===== ИНИЦИАЛИЗАЦИЯ =====
void initializeSensors() {
    Serial.println("Initializing sensor system...");
    
    // Настройка пинов датчиков D1-D14
    for (int i = 1; i <= 14; i++) {
        pinMode(sensor_pins[i], INPUT_PULLUP);
        sensor_states[i] = false;
    }
    
    // Настройка пинов энкодеров
    pinMode(ENC1_ENABLE, OUTPUT);
    pinMode(ENC2_ENABLE, OUTPUT);
    
    // Выключить энкодеры по умолчанию
    digitalWrite(ENC1_ENABLE, LOW);
    digitalWrite(ENC2_ENABLE, LOW);
    
    sensors_initialized = true;
    Serial.println("Sensor system initialized OK");
    
    // Первоначальное чтение всех датчиков
    bool initial_states[15];
    readAllSensors(initial_states);
    
    Serial.println("Initial sensor states:");
    for (int i = 1; i <= 14; i++) {
        Serial.print("D");
        Serial.print(i);
        Serial.print(": ");
        Serial.println(initial_states[i] ? "ACTIVE" : "INACTIVE");
    }
}

// ===== ЧТЕНИЕ ДАТЧИКОВ =====
bool readSensor(int sensor_number) {
    if (sensor_number < 1 || sensor_number > 14) {
        Serial.print("ERROR: Invalid sensor number: ");
        Serial.println(sensor_number);
        return false;
    }
    
    if (!sensors_initialized) {
        Serial.println("ERROR: Sensors not initialized!");
        return false;
    }
    
    // Читаем датчик (инвертируем, так как используем INPUT_PULLUP)
    bool state = !digitalRead(sensor_pins[sensor_number]);
    
    // Обновляем кэш состояния
    sensor_states[sensor_number] = state;
    
    return state;
}

void readAllSensors(bool* sensor_states_out) {
    if (!sensors_initialized) {
        Serial.println("ERROR: Sensors not initialized!");
        return;
    }
    
    for (int i = 1; i <= 14; i++) {
        sensor_states_out[i] = readSensor(i);
    }
}

// ===== ЭНКОДЕРЫ =====
void enableEncoder(int encoder_number) {
    if (encoder_number == 1) {
        digitalWrite(ENC1_ENABLE, HIGH);
        Serial.println("Encoder 1 ENABLED");
    } else if (encoder_number == 2) {
        digitalWrite(ENC2_ENABLE, HIGH);
        Serial.println("Encoder 2 ENABLED");
    } else {
        Serial.print("ERROR: Invalid encoder number: ");
        Serial.println(encoder_number);
    }
}

void disableEncoder(int encoder_number) {
    if (encoder_number == 1) {
        digitalWrite(ENC1_ENABLE, LOW);
        Serial.println("Encoder 1 DISABLED");
    } else if (encoder_number == 2) {
        digitalWrite(ENC2_ENABLE, LOW);
        Serial.println("Encoder 2 DISABLED");
    } else {
        Serial.print("ERROR: Invalid encoder number: ");
        Serial.println(encoder_number);
    }
}

uint16_t readEncoder(int encoder_number) {
    // Заглушка - в реальной системе здесь будет чтение энкодера через I2C или SPI
    Serial.print("Reading encoder ");
    Serial.print(encoder_number);
    Serial.println(" (placeholder)");
    return 0;
}

void resetEncoder(int encoder_number) {
    // Заглушка - сброс энкодера
    Serial.print("Resetting encoder ");
    Serial.print(encoder_number);
    Serial.println(" (placeholder)");
}

// ===== ДИАГНОСТИКА =====
void printSensorStatus() {
    Serial.println("=== SENSOR STATUS ===");
    Serial.print("Initialized: ");
    Serial.println(sensors_initialized ? "YES" : "NO");
    
    Serial.println("Sensor states:");
    for (int i = 1; i <= 14; i++) {
        bool state = readSensor(i);
        Serial.print("D");
        Serial.print(i);
        Serial.print(": ");
        Serial.println(state ? "ACTIVE" : "INACTIVE");
    }
    
    Serial.print("Encoder 1: ");
    Serial.println(digitalRead(ENC1_ENABLE) ? "ENABLED" : "DISABLED");
    Serial.print("Encoder 2: ");
    Serial.println(digitalRead(ENC2_ENABLE) ? "ENABLED" : "DISABLED");
    
    Serial.println("====================");
}

void testAllSensors() {
    Serial.println("=== TESTING ALL SENSORS ===");
    
    for (int i = 1; i <= 14; i++) {
        Serial.print("Testing sensor D");
        Serial.print(i);
        Serial.print("... ");
        
        bool state = readSensor(i);
        Serial.println(state ? "ACTIVE" : "INACTIVE");
        
        delay(100);
    }
    
    Serial.println("SENSOR TEST COMPLETED");
}

bool checkSafetyConditions() {
    // Проверка критических датчиков безопасности
    // Например, проверить что все моторы в безопасных позициях
    
    bool safe = true;
    
    // Проверить основные позиционные датчики
    // Это примерная логика - нужно адаптировать под реальную систему
    
    if (!readSensor(1)) {
        Serial.println("WARNING: M3 not in safe position (D1)");
        safe = false;
    }
    
    if (!readSensor(9)) {
        Serial.println("WARNING: M4 not in safe position (D9)");
        safe = false;
    }
    
    return safe;
}

// ===== УТИЛИТЫ =====
bool waitForSensor(int sensor_number, uint16_t timeout_ms) {
    if (sensor_number < 1 || sensor_number > 14) {
        Serial.println("ERROR: Invalid sensor number for wait");
        return false;
    }
    
    Serial.print("Waiting for sensor D");
    Serial.print(sensor_number);
    Serial.print(" (timeout: ");
    Serial.print(timeout_ms);
    Serial.println("ms)");
    
    uint32_t start_time = millis();
    
    while (millis() - start_time < timeout_ms) {
        if (readSensor(sensor_number)) {
            Serial.print("Sensor D");
            Serial.print(sensor_number);
            Serial.println(" activated");
            return true;
        }
        delay(10); // Небольшая задержка для снижения нагрузки
    }
    
    Serial.print("TIMEOUT: Sensor D");
    Serial.print(sensor_number);
    Serial.println(" not activated");
    return false;
}

bool waitForSensorRelease(int sensor_number, uint16_t timeout_ms) {
    if (sensor_number < 1 || sensor_number > 14) {
        Serial.println("ERROR: Invalid sensor number for wait release");
        return false;
    }
    
    Serial.print("Waiting for sensor D");
    Serial.print(sensor_number);
    Serial.println(" release");
    
    uint32_t start_time = millis();
    
    while (millis() - start_time < timeout_ms) {
        if (!readSensor(sensor_number)) {
            Serial.print("Sensor D");
            Serial.print(sensor_number);
            Serial.println(" released");
            return true;
        }
        delay(10);
    }
    
    Serial.print("TIMEOUT: Sensor D");
    Serial.print(sensor_number);
    Serial.println(" not released");
    return false;
}
