/*
 * CORDON-82 v15.0a Arduino Edition
 * ЗАГОЛОВОЧНЫЙ ФАЙЛ УПРАВЛЕНИЯ МОТОРАМИ
 * 
 * Объявления функций для управления шаговыми моторами M1-M6 и DC мотором M7
 */

#ifndef MOTOR_CONTROL_H
#define MOTOR_CONTROL_H

#include <Arduino.h>
#include "config.h"

// ===== ИНИЦИАЛИЗАЦИЯ =====
void initializeMotors();

// ===== БАЗОВЫЕ ФУНКЦИИ УПРАВЛЕНИЯ =====
void selectMotor(int motor);
void enableMotorDriver();
void disableMotorDriver();

// ===== ОСНОВНАЯ ФУНКЦИЯ ДВИЖЕНИЯ =====
void motorStepRaw(int motor, int direction, int steps, int pulse_time, int pause_time, bool use_microseconds = false);

// ===== ФУНКЦИИ M7 (DC МОТОР) =====
void m7Stop();
void m7GoLeft();
void m7GoRight();

// ===== ВЫСОКОУРОВНЕВЫЕ ФУНКЦИИ =====
void testMotor(int motor);
void emergencyStopAll();
void softStopAll();

// ===== ДИАГНОСТИЧЕСКИЕ ФУНКЦИИ =====
bool isMotorSystemReady();
uint32_t getLastMotorOperationTime();
void printMotorStatus();

#endif // MOTOR_CONTROL_H
