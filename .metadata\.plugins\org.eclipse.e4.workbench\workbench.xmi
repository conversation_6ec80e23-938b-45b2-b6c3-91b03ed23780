<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_EZ0owEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_EZ0owUB8EfCiQ4wRMsUgSQ" bindingContexts="_EZ0oykB8EfCiQ4wRMsUgSQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;C.ioc&quot; tooltip=&quot;C/C.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/C/C.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_EZ0owUB8EfCiQ4wRMsUgSQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_FgrjIUB8EfCiQ4wRMsUgSQ" x="156" y="156" width="1050" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1748959819348"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_FgrjIUB8EfCiQ4wRMsUgSQ" selectedElement="_FgsKMEB8EfCiQ4wRMsUgSQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_FgsKMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_FzM2gEB8EfCiQ4wRMsUgSQ">
        <children xsi:type="advanced:Perspective" xmi:id="_FzM2gEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_FzM2gUB8EfCiQ4wRMsUgSQ" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_FzM2gUB8EfCiQ4wRMsUgSQ" selectedElement="_FzM2hkB8EfCiQ4wRMsUgSQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_FzM2gkB8EfCiQ4wRMsUgSQ" elementId="topLeft" containerData="2500" selectedElement="_FzM2g0B8EfCiQ4wRMsUgSQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_FzM2g0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_Fy24QEB8EfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_FzM2hEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_Fy5UgEB8EfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_FzM2hUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_Fy5UgUB8EfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_FzM2hkB8EfCiQ4wRMsUgSQ" containerData="7500" selectedElement="_FzM2jUB8EfCiQ4wRMsUgSQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_FzM2h0B8EfCiQ4wRMsUgSQ" containerData="7500" selectedElement="_FzM2iUB8EfCiQ4wRMsUgSQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_FzM2iEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_FyoOwEB8EfCiQ4wRMsUgSQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_FzM2iUB8EfCiQ4wRMsUgSQ" elementId="topRight" containerData="2500" selectedElement="_FzM2jEB8EfCiQ4wRMsUgSQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2ikB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_Fy7JsUB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2i0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_FzGI0EB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2jEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_FzKaQEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_FzM2jUB8EfCiQ4wRMsUgSQ" containerData="2500" selectedElement="_FzM2jkB8EfCiQ4wRMsUgSQ" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_FzM2jkB8EfCiQ4wRMsUgSQ" elementId="bottom" containerData="5000" selectedElement="_FzM2j0B8EfCiQ4wRMsUgSQ">
                  <tags>active</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2j0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" ref="_Fy5UgkB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2kEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" ref="_Fy57kEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2kUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_Fy6ioEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2kkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" ref="_Fy7JsEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_FzM2k0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack" containerData="5000" selectedElement="_FzM2lEB8EfCiQ4wRMsUgSQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2lEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" ref="_FzLBUEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2lUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" ref="_FzLoYEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FzM2lkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" ref="_FzMPcEB8EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_8L3HAEC2EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.perspective" selectedElement="_8L3HAUC2EfCiQ4wRMsUgSQ" label="Device Configuration Tool" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_8L3HAUC2EfCiQ4wRMsUgSQ" selectedElement="_8L3HBEC2EfCiQ4wRMsUgSQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_8L3HAkC2EfCiQ4wRMsUgSQ" elementId="left" containerData="1100" selectedElement="_8L3HA0C2EfCiQ4wRMsUgSQ">
              <tags>noFocus</tags>
              <children xsi:type="advanced:Placeholder" xmi:id="_8L3HA0C2EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_Fy24QEB8EfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
                <tags>active</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_8L3HBEC2EfCiQ4wRMsUgSQ" containerData="8900" selectedElement="_8L3HBUC2EfCiQ4wRMsUgSQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_8L3HBUC2EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editorss" containerData="9000" ref="_FyoOwEB8EfCiQ4wRMsUgSQ"/>
              <children xsi:type="basic:PartSashContainer" xmi:id="_8L3HBkC2EfCiQ4wRMsUgSQ" toBeRendered="false" containerData="1000">
                <children xsi:type="basic:PartStack" xmi:id="_8L3uEEC2EfCiQ4wRMsUgSQ" elementId="topRight" toBeRendered="false" containerData="5000"/>
                <children xsi:type="basic:PartStack" xmi:id="_8L3uEUC2EfCiQ4wRMsUgSQ" elementId="bottomRight" toBeRendered="false" containerData="5000">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8L3uEkC2EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.views.OutputsView" toBeRendered="false" ref="_8LxncEC2EfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Device Configuration Tool</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_FgsKMUB8EfCiQ4wRMsUgSQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_FgsKMkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_Fgn4wEB8EfCiQ4wRMsUgSQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_FgsKM0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_Fgq8EEB8EfCiQ4wRMsUgSQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_FgsKNEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_FgrjIEB8EfCiQ4wRMsUgSQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fgn4wEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fgq8EEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Information Center" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;file:///C:/ST/STM32CubeIDE_1.18.1/STM32CubeIDE/configuration/org.eclipse.osgi/73/0/.cp/welcome/index.html&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Gf9O0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Gf9O0UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FgrjIEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_FyoOwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editorss" selectedElement="_FyoOwUB8EfCiQ4wRMsUgSQ">
      <children xsi:type="basic:PartStack" xmi:id="_FyoOwUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy24QEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_F_6c0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_F_6c0UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy5UgEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy5UgUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy5UgkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_GNEvEEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_GNEvEUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy57kEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.priorityField=&quot;30&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Gogt4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Gogt4UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy6ioEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Gq2QMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Gq2QMUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy7JsEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_GshrMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_GshrMUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Fy7JsUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_GKwa4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_GKwa4UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FzGI0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FzKaQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view horizontalPosition=&quot;0&quot; verticalPosition=&quot;0&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;container path=&quot;/15&quot;/>&#xD;&#xA;&lt;container path=&quot;/15/RTE&quot;/>&#xD;&#xA;&lt;container path=&quot;/15/RTE/Device&quot;/>&#xD;&#xA;&lt;container path=&quot;/C&quot;/>&#xD;&#xA;&lt;sourceContainer path=&quot;/C/Core&quot;/>&#xD;&#xA;&lt;sourceContainer path=&quot;/C/Drivers&quot;/>&#xD;&#xA;&lt;container path=&quot;/C/Drivers/CMSIS&quot;/>&#xD;&#xA;&lt;container path=&quot;/C/Drivers/CMSIS/Device&quot;/>&#xD;&#xA;&lt;container path=&quot;/C/Drivers/STM32F1xx_HAL_Driver&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;selection>&#xD;&#xA;&lt;container path=&quot;/C/Drivers/CMSIS&quot;/>&#xD;&#xA;&lt;/selection>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
      <menus xmi:id="_Gu5CsEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Gu5CsUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FzLBUEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_GP710EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_GP710UB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FzLoYEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_GxSPYEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_GxSPYUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FzMPcEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_Gz9v8EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Gz-XAEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8LxncEC2EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.views.OutputsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
      <tags>View</tags>
      <tags>categoryTag:Device Configuration Tool</tags>
    </sharedElements>
    <trimBars xmi:id="_EZ0owkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_FoVqUEB8EfCiQ4wRMsUgSQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FoVqUUB8EfCiQ4wRMsUgSQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FoeNMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FopMUEB8EfCiQ4wRMsUgSQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_EbkVSkB8EfCiQ4wRMsUgSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FoeNMUB8EfCiQ4wRMsUgSQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FoeNMkB8EfCiQ4wRMsUgSQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Foe0QEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ForBgEB8EfCiQ4wRMsUgSQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_EbkVOEB8EfCiQ4wRMsUgSQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FosPoEB8EfCiQ4wRMsUgSQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_Ebmxf0B8EfCiQ4wRMsUgSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Foe0QUB8EfCiQ4wRMsUgSQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Foe0QkB8EfCiQ4wRMsUgSQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_F2TN0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_F5QbMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_F3_28EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_F1YAwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FofbUEB8EfCiQ4wRMsUgSQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FofbUUB8EfCiQ4wRMsUgSQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FofbUkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_FovS8EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_EbgDyEB8EfCiQ4wRMsUgSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FofbU0B8EfCiQ4wRMsUgSQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FofbVEB8EfCiQ4wRMsUgSQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FogCYEB8EfCiQ4wRMsUgSQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FogCYUB8EfCiQ4wRMsUgSQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FogCYkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Ft9xMEB8EfCiQ4wRMsUgSQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FuANcEB8EfCiQ4wRMsUgSQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_EZ0ow0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_EZ0oxEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_EZ0oxUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_EZ0oxkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_EZ0ox0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_GkZC4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_EZ0oyEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_EZ0oyUB8EfCiQ4wRMsUgSQ" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_EZ0oykB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcpTQkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+1" command="_EbgDwUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhZEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+L" command="_EbljZkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrIdUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SPACE" command="_EbjuKEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwA9kB8EfCiQ4wRMsUgSQ" keySequence="CTRL+V" command="_EbZ9I0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2IUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+SPACE" command="_EbjHGUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2IkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+A" command="_EbmKb0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcydMkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+C" command="_EbYvBUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrVUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+X" command="_EbkVQEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrVkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+Y" command="_Ebmxf0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec0SYUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+Z" command="_EbkVOEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec5x8EB8EfCiQ4wRMsUgSQ" keySequence="ALT+PAGE_UP" command="_EbmxlUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec5x8UB8EfCiQ4wRMsUgSQ" keySequence="ALT+PAGE_DOWN" command="_EbcZYUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZAEB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+INSERT" command="_EbZ9I0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZBUB8EfCiQ4wRMsUgSQ" keySequence="ALT+F11" command="_EbbLT0B8EfCiQ4wRMsUgSQ">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_Ec9cVEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F10" command="_EbakPUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DYkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+INSERT" command="_EbYvBUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qdEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+PAGE_UP" command="_Ebk8VEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qdUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+PAGE_DOWN" command="_EbgDyUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qd0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+F3" command="_Ebk8RkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec_Rg0B8EfCiQ4wRMsUgSQ" keySequence="SHIFT+DEL" command="_EbkVQEB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Ece7MEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_EbutQkB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcmP8EB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+CR" command="_Ebk8REB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcoFIEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+BS" command="_EbXg5kB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTQUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+Q" command="_EbdnkUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhYEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+J" command="_EbdngUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhYkB8EfCiQ4wRMsUgSQ" keySequence="CTRL++" command="_EbbLTUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhZ0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+-" command="_EbljaUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecuy10B8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+J" command="_EbfcskB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwA8EB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+A" command="_EbZWH0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwoBEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+J" command="_EbakQ0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcxPEEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+L" command="_EbkVM0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcydNkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+D" command="_EbbLQUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrUEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+=" command="_EbbLTUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrU0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Y" command="_EbW50UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec0SZEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+DEL" command="_EbjuLUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec0SZUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+X" command="_EbYvEEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec0SZkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+Y" command="_EbljaEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec05c0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+DEL" command="_EbkVNUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec05eEB8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_UP" command="_EbmxkUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec1ggEB8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_DOWN" command="_EbdAdkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec5x8kB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+END" command="_Ebljb0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZBkB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+HOME" command="_EbljVEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AFUB8EfCiQ4wRMsUgSQ" keySequence="END" command="_Ebk8YEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AFkB8EfCiQ4wRMsUgSQ" keySequence="INSERT" command="_EbakNUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AGEB8EfCiQ4wRMsUgSQ" keySequence="F2" command="_EbgDy0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8OMUB8EfCiQ4wRMsUgSQ" keySequence="HOME" command="_EbljcUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8ONEB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_UP" command="_EbmKikB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8ONUB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_EbmKfEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81QEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+INSERT" command="_EbcZZUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81RUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_EbljckB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81RkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_EbdAeUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec9cVUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F10" command="_Ebk8QEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DYEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+END" command="_EbdAfEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qcEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_UP" command="_EbbyVkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qcUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_DOWN" command="_EbnYiUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qckB8EfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_LEFT" command="_EbXg50B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qc0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_RIGHT" command="_EbdnjkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qdkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+HOME" command="_EbZ9IkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-qeEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_EbdnkEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec_RgEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_ADD" command="_EbmKd0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec_RgUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_Ebk8QkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec_RgkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_EbbyWUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU0EB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_EbfcsEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU0kB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_EbakNkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdEKAEB8EfCiQ4wRMsUgSQ" keySequence="ALT+/" command="_EbmKYEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdEKAkB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+CR" command="_EbljcEB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcneEEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_EbutVEB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcneEUB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+SHIFT+C" command="_EbnYi0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcoFIUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+TAB" command="_EbnYhUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcoFJEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+P" command="_Ebk8YkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTSEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+T" command="_EbjHGEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecp6U0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+7" command="_EbZ9JkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecp6WEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+H" command="_EbakRkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhaEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+N" command="_EbdnjUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrIc0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+/" command="_EbZ9JkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrIdEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+O" command="_EbkVUEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrIdkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+A" command="_Ebk8R0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrIeEB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+S" command="_EbmxhUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrvgEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+#" command="_Ebh4-UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrvgUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+C" command="_EbZ9JkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcuLwEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F" command="_EbnYhkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcuLwUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+G" command="_EbXg4UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcuLxEB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+H" command="_EbgDxEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecuy0UB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+I" command="_EbZ9KkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwA9EB8EfCiQ4wRMsUgSQ" keySequence="CTRL+T" command="_EbdAgEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwoA0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+I" command="_EbdAhUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcxPFUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+/" command="_EbmKdkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2IEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+O" command="_EbZWFEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2I0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+R" command="_EbjuMkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2JkB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+S" command="_EbakPkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcydM0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+T" command="_EbjHEEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczEQ0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_EbmxekB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczER0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+L" command="_EbmKfkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczESEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+M" command="_EbZWIEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczESUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+=" command="_Ebh4-UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrUkB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+O" command="_EbkVQkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrVEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Z" command="_EbkVQ0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec05ckB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+\" command="_EbZ9J0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nIEB8EfCiQ4wRMsUgSQ" keySequence="F3" command="_EbnYj0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nJkB8EfCiQ4wRMsUgSQ" keySequence="F4" command="_EbmKcEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81Q0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_EbjuNkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81REB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_EbcZY0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec9cUEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_UP" command="_EbljV0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec9cUkB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_EbnYh0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec9cU0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_EbjuKUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec9cVkB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_EbljXkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU1kB8EfCiQ4wRMsUgSQ" keySequence="ALT+C" command="_EbZ9KEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdEKAUB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+TAB" command="_EbakSEB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcoFIkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_EZ0oy0B8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcoFI0B8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+SHIFT+L" command="_EbZ9MEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcosMEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q O" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_EcpTQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_EcpTQ0B8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+B" command="_EbbyZUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTREB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+R" command="_EbnYjUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTRUB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q Q" command="_EbbyXUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTRkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+S" command="_EbbLRkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTR0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+3" command="_EbgDykB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcpTS0B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q S" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ecp6UEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_Ecp6VEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q V" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ecp6VUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_Ecp6VkB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+G" command="_EbbLWkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecp6V0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+W" command="_EbkVP0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecp6WkB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q H" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ecp6W0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_EcqhYUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+K" command="_EbbyUEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhY0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+," command="_EbZ9KUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcqhZUB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q L" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_EcqhZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_EcrIcEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+." command="_EbmKjUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrId0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+B" command="_EbbyUUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecrvg0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+E" command="_EbbybEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecuy00B8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q X" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ecuy1EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_Ecuy1UB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q Y" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ecuy1kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_EcvZ4EB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q Z" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_EcvZ4UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_EcvZ5UB8EfCiQ4wRMsUgSQ" keySequence="CTRL+P" command="_EbkVSkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcvZ5kB8EfCiQ4wRMsUgSQ" keySequence="CTRL+Q" command="_Ebk8TkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwA80B8EfCiQ4wRMsUgSQ" keySequence="CTRL+S" command="_EbljakB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwoAUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+W" command="_EbmxikB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwoAkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+H" command="_EbjuJkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwoBUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+K" command="_EbbyZ0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcxPEUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+M" command="_EbjHJkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcxPFEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+N" command="_EbmxhkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2JUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+B" command="_EbZ9LUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcydMEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q B" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_EcydMUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_EcydNEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q C" command="_EbbyXUB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_EcydNUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_EcydN0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+E" command="_EbkVMUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczEQEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F" command="_EbbLTEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczERkB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+W" command="_EbmKiUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczrUUB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+N" command="_EbkVO0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec0SYkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+_" command="_EbjHHkB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ec0SY0B8EfCiQ4wRMsUgSQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_Ec05cEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+{" command="_EbjHHkB8EfCiQ4wRMsUgSQ">
      <parameters xmi:id="_Ec05cUB8EfCiQ4wRMsUgSQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_Ec05eUB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+F9" command="_EbkVTUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec1gg0B8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_LEFT" command="_EbakQUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec1ghkB8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_RIGHT" command="_Ebk8RUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZAkB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+F5" command="_EbmKhEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZBEB8EfCiQ4wRMsUgSQ" keySequence="ALT+F7" command="_EbZ9JEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AEkB8EfCiQ4wRMsUgSQ" keySequence="F9" command="_Ebk8ZkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AE0B8EfCiQ4wRMsUgSQ" keySequence="F11" command="_EbmKbEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AFEB8EfCiQ4wRMsUgSQ" keySequence="F12" command="_EbjuK0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AF0B8EfCiQ4wRMsUgSQ" keySequence="F2" command="_EbZ9K0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nKUB8EfCiQ4wRMsUgSQ" keySequence="F5" command="_Ebk8YUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8OMkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F7" command="_EbmKbUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8OM0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F8" command="_EbjHHEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8ONkB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_Ebk8TkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8ON0B8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_EbbyW0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81QUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F4" command="_EbkVP0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81QkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F6" command="_EbbLS0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec81R0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+F7" command="_EbYvBkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec9cUUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F8" command="_EbgDw0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DZkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F4" command="_EbmxikB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DaEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F6" command="_EbbLU0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DaUB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+F7" command="_EbdAf0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdBtwUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_EbbLRUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU0UB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_EbjHIUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU00B8EfCiQ4wRMsUgSQ" keySequence="DEL" command="_EbbLWEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi-EB8EfCiQ4wRMsUgSQ" keySequence="ALT+-" command="_EbZWF0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdEKA0B8EfCiQ4wRMsUgSQ" keySequence="ALT+CR" command="_Ebh4-kB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcoFJUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_EbutS0B8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcoFJkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+P" command="_EbljW0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcuLw0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+G" command="_Ebk8X0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nJUB8EfCiQ4wRMsUgSQ" keySequence="F3" command="_Ebk8T0B8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcpTSUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_EbutVUB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcpTSkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+T" command="_EbjHGEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecp6WUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+H" command="_EbakRkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcuLwkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+G" command="_EbXg4UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecuy0EB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+H" command="_EbgDxEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecuy0kB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+I" command="_EbZ9KkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ecx2JEB8EfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+R" command="_EbjuMkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczEREB8EfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_EbmxekB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nI0B8EfCiQ4wRMsUgSQ" keySequence="F3" command="_EbnYj0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nJ0B8EfCiQ4wRMsUgSQ" keySequence="F4" command="_EbmKcEB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Ecp6UUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_EbutQEB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_Ecp6UkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+V" command="_EbljWkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcrvgkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+C" command="_Ebh48UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec05d0B8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_UP" command="_EbXg5UB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec1ghUB8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_RIGHT" command="_EbmKaUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec5x80B8EfCiQ4wRMsUgSQ" keySequence="SHIFT+INSERT" command="_EbljWkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DYUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+INSERT" command="_Ebh48UB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcrIcUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_EbutU0B8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcrIckB8EfCiQ4wRMsUgSQ" keySequence="CTRL+/" command="_EbbyYkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AGUB8EfCiQ4wRMsUgSQ" keySequence="F3" command="_EbakS0B8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcvZ4kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_EbutSkB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcvZ40B8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+M" command="_EbmKbkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcvZ5EB8EfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+N" command="_EbmKe0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwA9UB8EfCiQ4wRMsUgSQ" keySequence="CTRL+T" command="_Ebh49EB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcwoAEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+W" command="_EbakO0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcxPE0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+N" command="_EbbLUEB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcwA8UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_EbutTEB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcwA8kB8EfCiQ4wRMsUgSQ" keySequence="CTRL+R" command="_EbYvDEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AEEB8EfCiQ4wRMsUgSQ" keySequence="F7" command="_EbmxdkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7AEUB8EfCiQ4wRMsUgSQ" keySequence="F8" command="_EbakMUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nKEB8EfCiQ4wRMsUgSQ" keySequence="F5" command="_EbakMkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nKkB8EfCiQ4wRMsUgSQ" keySequence="F6" command="_EbmKZEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DZUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+F2" command="_EbjuLkB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EcwoBkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_EbutTUB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EcwoB0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+," command="_Ebk8UEB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EcxPEkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+." command="_EbjHI0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EczERUB8EfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_EbjHJEB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EczEQUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_EbutUEB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EczEQkB8EfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_EbmxgkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec8OMEB8EfCiQ4wRMsUgSQ" keySequence="HOME" command="_EbgDzUB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EczrV0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.console" bindingContext="_EbutREB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_Ec0SYEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+Z" command="_EbmKh0B8EfCiQ4wRMsUgSQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_Ec05dEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_EbutVkB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_Ec05dUB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+F7" command="_EbakSkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec05dkB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+F8" command="_EbjHG0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZAUB8EfCiQ4wRMsUgSQ" keySequence="SHIFT+F5" command="_EbmKZUB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec6ZA0B8EfCiQ4wRMsUgSQ" keySequence="SHIFT+F6" command="_EbZ9LkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec-DZ0B8EfCiQ4wRMsUgSQ" keySequence="CTRL+F5" command="_EbmKYUB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Ec1ggUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_EbutR0B8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_Ec1ggkB8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_LEFT" command="_EbdAgkB8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec1ghEB8EfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_RIGHT" command="_Ebe1o0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_Ec7nJEB8EfCiQ4wRMsUgSQ" keySequence="F3" command="_EbnYj0B8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Ec7nIUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_EbutSUB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_Ec7nIkB8EfCiQ4wRMsUgSQ" keySequence="F3" command="_EbnYj0B8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Ec-DY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_EbutSEB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_Ec-DZEB8EfCiQ4wRMsUgSQ" keySequence="CTRL+INSERT" command="_EbbLREB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Ec_RhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_EbutUkB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EdBtwEB8EfCiQ4wRMsUgSQ" keySequence="ALT+Y" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU1EB8EfCiQ4wRMsUgSQ" keySequence="ALT+A" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdCU1UB8EfCiQ4wRMsUgSQ" keySequence="ALT+B" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC74EB8EfCiQ4wRMsUgSQ" keySequence="ALT+C" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC74UB8EfCiQ4wRMsUgSQ" keySequence="ALT+D" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC74kB8EfCiQ4wRMsUgSQ" keySequence="ALT+E" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC740B8EfCiQ4wRMsUgSQ" keySequence="ALT+F" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC75EB8EfCiQ4wRMsUgSQ" keySequence="ALT+G" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC75UB8EfCiQ4wRMsUgSQ" keySequence="ALT+P" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC75kB8EfCiQ4wRMsUgSQ" keySequence="ALT+R" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdC750B8EfCiQ4wRMsUgSQ" keySequence="ALT+S" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi8EB8EfCiQ4wRMsUgSQ" keySequence="ALT+T" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi8UB8EfCiQ4wRMsUgSQ" keySequence="ALT+V" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi8kB8EfCiQ4wRMsUgSQ" keySequence="ALT+W" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi80B8EfCiQ4wRMsUgSQ" keySequence="ALT+H" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi9kB8EfCiQ4wRMsUgSQ" keySequence="ALT+L" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_EdDi90B8EfCiQ4wRMsUgSQ" keySequence="ALT+N" command="_EbZWE0B8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_EdDi9EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.context" bindingContext="_EbutUUB8EfCiQ4wRMsUgSQ">
    <bindings xmi:id="_EdDi9UB8EfCiQ4wRMsUgSQ" keySequence="ALT+K" command="_EbdAcUB8EfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_FyqrAUB8EfCiQ4wRMsUgSQ" bindingContext="_FyqrAEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyrSEUB8EfCiQ4wRMsUgSQ" bindingContext="_FyrSEEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyr5IUB8EfCiQ4wRMsUgSQ" bindingContext="_Fyr5IEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FysgMUB8EfCiQ4wRMsUgSQ" bindingContext="_FysgMEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FysgM0B8EfCiQ4wRMsUgSQ" bindingContext="_FysgMkB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FytHQUB8EfCiQ4wRMsUgSQ" bindingContext="_FytHQEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FytHQ0B8EfCiQ4wRMsUgSQ" bindingContext="_FytHQkB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FytHRUB8EfCiQ4wRMsUgSQ" bindingContext="_FytHREB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FytuUUB8EfCiQ4wRMsUgSQ" bindingContext="_FytuUEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FytuU0B8EfCiQ4wRMsUgSQ" bindingContext="_FytuUkB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyuVYEB8EfCiQ4wRMsUgSQ" bindingContext="_FytuVEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyuVYkB8EfCiQ4wRMsUgSQ" bindingContext="_FyuVYUB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyuVZEB8EfCiQ4wRMsUgSQ" bindingContext="_FyuVY0B8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyu8cEB8EfCiQ4wRMsUgSQ" bindingContext="_FyuVZUB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyu8ckB8EfCiQ4wRMsUgSQ" bindingContext="_Fyu8cUB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyu8dEB8EfCiQ4wRMsUgSQ" bindingContext="_Fyu8c0B8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyu8dkB8EfCiQ4wRMsUgSQ" bindingContext="_Fyu8dUB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyvjgUB8EfCiQ4wRMsUgSQ" bindingContext="_FyvjgEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyvjg0B8EfCiQ4wRMsUgSQ" bindingContext="_FyvjgkB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyvjhUB8EfCiQ4wRMsUgSQ" bindingContext="_FyvjhEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FywKkUB8EfCiQ4wRMsUgSQ" bindingContext="_FywKkEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FywKk0B8EfCiQ4wRMsUgSQ" bindingContext="_FywKkkB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FywKlUB8EfCiQ4wRMsUgSQ" bindingContext="_FywKlEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FywxoUB8EfCiQ4wRMsUgSQ" bindingContext="_FywxoEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fywxo0B8EfCiQ4wRMsUgSQ" bindingContext="_FywxokB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FywxpUB8EfCiQ4wRMsUgSQ" bindingContext="_FywxpEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyxYsUB8EfCiQ4wRMsUgSQ" bindingContext="_FyxYsEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyxYs0B8EfCiQ4wRMsUgSQ" bindingContext="_FyxYskB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_FyxYtUB8EfCiQ4wRMsUgSQ" bindingContext="_FyxYtEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyx_wUB8EfCiQ4wRMsUgSQ" bindingContext="_Fyx_wEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyx_w0B8EfCiQ4wRMsUgSQ" bindingContext="_Fyx_wkB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyym0EB8EfCiQ4wRMsUgSQ" bindingContext="_Fyx_xEB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyym0kB8EfCiQ4wRMsUgSQ" bindingContext="_Fyym0UB8EfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_Fyym1EB8EfCiQ4wRMsUgSQ" bindingContext="_Fyym00B8EfCiQ4wRMsUgSQ"/>
  <rootContext xmi:id="_EZ0oykB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_EZ0oy0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_EZ0ozEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_EbutQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_EbutQUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_EbutQkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_EbutSUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_EbutS0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_EbutU0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_EbutVEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_EbutREB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_EbutRUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_EbutSEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_EbutSkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_EbutTEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_EbutTUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_EbutTkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mpu.debug.ui.debugging" name="Debugging C/C++ on MPU - Cortex-M" description="Debugging C/C++ Programs on MPU - Cortex-M"/>
        <children xmi:id="_EbutT0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.ui.debugging" name="Debugging C/C++ on MCU" description="Debugging C/C++ Programs on MCU"/>
        <children xmi:id="_EbutUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_EbutVkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_EbutUkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_EbutVUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
    </children>
    <children xmi:id="_EZ0ozUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_EbutR0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_EbutQ0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_EbutRkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_EbutUUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.context" name="Device Configuration Tool Context" description="Device Configuration Tool  Context"/>
  <rootContext xmi:id="_FyqrAEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet" name="Auto::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet"/>
  <rootContext xmi:id="_FyrSEEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.informationcenter.actionSet3" name="Auto::com.st.stm32cube.ide.mcu.informationcenter.actionSet3"/>
  <rootContext xmi:id="_Fyr5IEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_FysgMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_FysgMkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_FytHQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_FytHQkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_FytHREB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_FytuUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_FytuUkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_FytuVEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_FyuVYUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_FyuVY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_FyuVZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_Fyu8cUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_Fyu8c0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_Fyu8dUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_FyvjgEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_FyvjgkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_FyvjhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_FywKkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_FywKkkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_FywKlEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_FywxoEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_FywxokB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_FywxpEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_FyxYsEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_FyxYskB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_FyxYtEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_Fyx_wEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_Fyx_wkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_Fyx_xEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_Fyym0UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_Fyym00B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_EsSRwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_FfAvMEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.views.OutputsView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" category="Device Configuration Tool" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
    <tags>View</tags>
    <tags>categoryTag:Device Configuration Tool</tags>
  </descriptors>
  <descriptors xmi:id="_FfFAoEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FfG10EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FfID8EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.logview" label="SWV Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.SWVLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FfJSEEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.statisticalprofiling" label="SWV Statistical Profiling" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_statistical_profiling.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.statisticalprofiling.SWVStatisticalProfilingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FfLHQEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.itmtrace" label="SWV ITM Data Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/console_view.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.itmtrace.SWVConsole"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FfLuUEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.exceptionlogview" label="SWV Exception Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_Exception_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.SWVExceptionLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FfMVYEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceview" label="SWV Data Trace" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/insp_sbook.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatrace.SWVDataTraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FfM8cEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.SWVDatatraceTimeline" label="SWV Data Trace Timeline Graph" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/Datatrace_timeline.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceTimeline.SWVDatatraceTimeline"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FfOKkEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.faultanalyzer.view" label="Fault Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.faultanalyzer/icons/clanbomber.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.faultanalyzer.FaultAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.faultanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfPYsEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.queues" label="FreeRTOS Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.queues.FORtosQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FfQm0EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.tasklist" label="FreeRTOS Task List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.tasklist.FORtosTaskList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FfRN4EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.semaphore" label="FreeRTOS Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.semaphores.FORtosSemaphores"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FfR08EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.timers" label="FreeRTOS Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.timers.FORtosTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FfU4QEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" label="Live Expressions" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfWtcEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.sfrview" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfX7kEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FfZwwEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.tcp.console.view" label="TCP Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.tcp.console/icons/console.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.tcp.console.ui.TCPConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.tcp.console"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_Ffa-4EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.threads" label="ThreadX Thread List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.threadlist.ThreadXThreadList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_Ffbl8EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.semaphores" label="ThreadX Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.semaphores.ThreadXSemaphoreList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FfepQEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.mutexes" label="ThreadX Mutexes" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.mutexes.ThreadXMutexList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FffQUEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.queues" label="ThreadX Message Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.queues.ThreadXMessageQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_Fff3YEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.eventflags" label="ThreadX Event Flags" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.eventflags.ThreadXEventFlags"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FfhFgEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.timer" label="ThreadX Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.timers.ThreadXTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FfjhwEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.blockpools" label="ThreadX Memory Block Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.blockpools.ThreadXMemoryBlockPools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FfkI0EB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.bytepools" label="ThreadX Memory Byte Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.bytepools.ThreadXMemoryBytePools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_Ffkv4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FflW8EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfnzMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfpBUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfpoYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Ffq2gEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FfrdkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FftSwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Fft50EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Ffw9IEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_FfyLQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_Ff0ngEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_Ff1OkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_Ff11oEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_Ff2csEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_Ff3DwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Ff3q0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Ff8jUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Ff9xcEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Ff-YgEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Ff-_kEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FgANsEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FgA0wEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FgBb0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FgCC4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FgFGMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_FgGUUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgG7YEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_FgJXoEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_FgJ-sEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgKlwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgLz4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FgO3MEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgPeQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgQsYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgR6gEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgShkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgTvsEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgVk4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgXaEEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgYoMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgZ2UEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FgbEcEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_EbW50EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbW50UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbW50kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg4EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg4UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg4kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_EbSodkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg40B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg5EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg5UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_EbSodEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg5kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg50B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg6EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_EbSoc0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbXg6UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvAEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_EbSobUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbYvAUB8EfCiQ4wRMsUgSQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_EbYvAkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvA0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbYvBEB8EfCiQ4wRMsUgSQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_EbYvBUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvBkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvB0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_EbSoYkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbYvCEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_EbYvCUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvCkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvC0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvDEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvDUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_EbTPckB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvDkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvD0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvEEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvEUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvEkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbYvE0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWEEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWEUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWEkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWE0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_EbSodEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWFEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWFUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_EbSoc0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWFkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWF0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWGEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWGUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWGkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_EbTPdEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWG0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_EbTPd0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWHEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWHUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWHkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWH0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWIEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWIUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWIkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWI0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWJEB8EfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_EbSoaUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWJUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mpu.linux.ide.command.setupopenstlinux" commandName="Setup OpenSTLinux" description="Setup OpenSTLinux" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZWJkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9IEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9IUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9IkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9I0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9JEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9JUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9JkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9J0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9KEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9KUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9KkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9K0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9LEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9LUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9LkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_EbTPdUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9L0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9MEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_EbSobEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9MUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9MkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_EbSoZkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9M0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.ide.command.generatecode" commandName="Generate Code" description="Generate Code (based on .ioc file content)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9NEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9NUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9NkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbZ9N0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_EbTPc0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_EbTPeEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakMUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakMkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakM0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakNEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakNUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakNkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakN0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakOEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakOUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbakOkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_EbakO0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakPEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakPUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakPkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakP0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakQUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakQkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakQ0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakREB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakRUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakRkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakR0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakSEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakSUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakSkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_EbTPdUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbakS0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_EbSodUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLQUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLQkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_EbTPckB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLQ0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartConfigurationCommand" commandName="Restart Configuration Command" category="_EbSobkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLREB8EfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_EbSoaUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLRUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLRkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLR0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.externaltools.test" commandName="Test ExternalTools" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLSEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLSUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLSkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLS0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLTEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLTUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLTkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLT0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLUUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLUkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_EbSoYEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLU0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLVEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLVUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLVkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.common.ui.view_export" commandName="Export view data to file" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLV0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLWEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLWUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbLWkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_EbSodkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyUUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyUkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_EbSod0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyU0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyVEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_EbTPdUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbbyVUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_EbbyVkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyV0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyWEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyWUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyWkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyW0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyXEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyXUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_EbSoYUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbbyXkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_EbbyX0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_EbbyYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_EbbyYUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyYkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_EbSodUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_EbSoaEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbbyZEB8EfCiQ4wRMsUgSQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_EbbyZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyZ0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyaEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyaUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbbyakB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_EbSoakB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebbya0B8EfCiQ4wRMsUgSQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_EbbybEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZYUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZYkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZZEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbcZZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_EbSocEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbdAcEB8EfCiQ4wRMsUgSQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_EbdAcUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.menu.generatecode" commandName="Generate Code" description="Generate Code" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAckB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAc0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAdEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAdUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAdkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAd0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.datarefresh" commandName="Data Refresh" description="Data Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAeEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAeUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAekB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAe0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAfEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAfUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_EbSod0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAfkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAf0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAgEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAgUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showInCommand" commandName="name" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAgkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAg0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdAhUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdngEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdngUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdngkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebdng0B8EfCiQ4wRMsUgSQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_EbdnhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnhUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnhkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_EbSoc0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebdnh0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_EbSoZkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdniEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_EbSoZ0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdniUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnikB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_EbSodkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebdni0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnjEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_EbSodkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnjUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnjkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebdnj0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnkUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbdnkkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbeOkEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showstate" commandName="name" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebe1oEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_Ebe1oUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebe1okB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebe1o0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebe1pEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebe1pUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.docsandresources" commandName="Docs And Resources" description="Docs And Resources" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebe1pkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_EbSocEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebe1p0B8EfCiQ4wRMsUgSQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_EbfcsEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbfcsUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbfcskB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebfcs0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbfctEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbfctUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbfctkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_EbSoZkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDwUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDwkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_EbSod0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDw0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDxEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDxUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDxkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.generatereport" commandName="Generate Report" description="Generate Report" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDx0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDyEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDyUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDykB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDy0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDzEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbgDzUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh48EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh48UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_EbSodEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh48kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh480B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh49EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh49UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh49kB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.informationcenter.tutorialvideo" commandName="Tutorial Video" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh490B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_EbTPc0B8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebh4-EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_Ebh4-UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebh4-kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbigAEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHEEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHEUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_EbSobUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbjHEkB8EfCiQ4wRMsUgSQ" elementId="url" name="URL"/>
    <parameters xmi:id="_EbjHE0B8EfCiQ4wRMsUgSQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_EbjHFEB8EfCiQ4wRMsUgSQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_EbjHFUB8EfCiQ4wRMsUgSQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_EbjHFkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHF0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHGEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHGUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHGkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHG0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_EbTPdUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHHEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHHUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.toolbar.generatecode" commandName="Generate Code" description="Generate Code" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHHkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_EbSobUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbjHH0B8EfCiQ4wRMsUgSQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_EbjHIEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHIUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHIkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHI0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHJEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHJUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjHJkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuIEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuIUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuIkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.start_trace" commandName="Start Trace" description="Start Trace" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuI0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuJEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuJUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuJkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_EbSodkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuJ0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.modifyIncludePathsBySelection" commandName="Add/remove include path..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuKEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuKUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuKkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuK0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuLEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuLUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuLkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuL0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuMUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuMkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuM0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_EbTPeEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuNEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuNUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuNkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbjuN0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_EbTPckB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbjuOEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_EbkVMEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_EbkVMUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVMkB8EfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_EbSoaUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVM0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVNEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVNUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVNkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVN0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVOEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVOUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVOkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVO0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVPEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVPUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVPkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVP0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVQEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVQUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVQkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVQ0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVREB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVRUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVRkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVR0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_EbSoakB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbkVSEB8EfCiQ4wRMsUgSQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_EbkVSUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_EbTPc0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVSkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVS0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp" commandName="Convert Project to C or CPP" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbkVTEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp.type" name="Convert Type (C/C++)"/>
  </commands>
  <commands xmi:id="_EbkVTUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVTkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVT0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbkVUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8QEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8QUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8QkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8Q0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8REB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8RUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8RkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8R0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8SEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8SUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.ide.connectionToMyST" commandName="Connection to myST" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8SkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mpu.remote.serial.connectconsole" commandName="Connect Console Command" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8S0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_EbSodkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8TEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8TUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8TkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8T0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8UEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8UUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8UkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_EbSod0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8U0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8VEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8VUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_EbTPcUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebk8VkB8EfCiQ4wRMsUgSQ" elementId="title" name="Title"/>
    <parameters xmi:id="_Ebk8V0B8EfCiQ4wRMsUgSQ" elementId="message" name="Message"/>
    <parameters xmi:id="_Ebk8WEB8EfCiQ4wRMsUgSQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_Ebk8WUB8EfCiQ4wRMsUgSQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_Ebk8WkB8EfCiQ4wRMsUgSQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_Ebk8W0B8EfCiQ4wRMsUgSQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_Ebk8XEB8EfCiQ4wRMsUgSQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_Ebk8XUB8EfCiQ4wRMsUgSQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_Ebk8XkB8EfCiQ4wRMsUgSQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_Ebk8X0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8YEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8YUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8YkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8Y0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8ZEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8ZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8ZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8Z0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8aEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebk8aUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljUEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljUUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljUkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_EbTPeEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljU0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_EbSoc0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljVEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljVUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljVkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljV0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljWEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljWUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartCommand" commandName="Restart Command" category="_EbSobkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljWkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_EbSodEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljW0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljXEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.checkforupdates" commandName="Check For Embedded Software Packages Updates" description="Check For Embedded Software Packages Updates" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljXUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljXkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljX0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_EbSoaEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbljYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_EbljYUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljYkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbljZEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_EbljZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_EbljZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljZ0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljaEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljaUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljakB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Eblja0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_EbTPeEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljbEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_EbSoYEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljbUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_EbTPd0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljbkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebljb0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljcEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljcUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbljckB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebljc0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKYUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKYkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_EbSoYEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKZEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_EbTPdUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKZ0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.trx_to_file_command" commandName="Export trace" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKaEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKaUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_EbSodEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKakB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_EbSoakB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbmKa0B8EfCiQ4wRMsUgSQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_EbmKbEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKbUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKbkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKb0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKcEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKcUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKckB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_EbSoZkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKc0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKdEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKdUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.cmake.cmake_run_builder" commandName="cmake_run_builder" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKdkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKd0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKeEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKeUB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.openconfig" commandName="Config" description="Configure SWV" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKekB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_EbTPdEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKe0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKfEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKfUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_EbSocUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKfkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKf0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKgEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKgUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKgkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKg0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKhUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKhkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKh0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKiEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_EbTPeEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKiUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKikB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKi0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.stlink.fwupgrade" commandName="ST-LINK Upgrade" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKjEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmKjUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxcEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_EbSobUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbmxcUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_EbmxckB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_EbSocEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebmxc0B8EfCiQ4wRMsUgSQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_EbmxdEB8EfCiQ4wRMsUgSQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_EbmxdUB8EfCiQ4wRMsUgSQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_EbmxdkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebmxd0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_EbSobUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbmxeEB8EfCiQ4wRMsUgSQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_EbmxeUB8EfCiQ4wRMsUgSQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_EbmxekB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebmxe0B8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.cmake.commands.cmakeimport" commandName="cmakeimport" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxfEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_EbSocEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxfUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_EbTPdEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxfkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebmxf0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxgEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.p2.list" commandName="P2 IU List" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxgUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_EbSob0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxgkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebmxg0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_EbSockB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxhUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxhkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_EbSoakB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebmxh0B8EfCiQ4wRMsUgSQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_EbmxiEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxiUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxikB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_EbSoakB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebmxi0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxjEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxjUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxjkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Ebmxj0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxkUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxkkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_EbTPdkB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebmxk0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_EbmxlEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_EbmxlUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_EbSoaEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbmxlkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_EbSocEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebmxl0B8EfCiQ4wRMsUgSQ" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_EbmxmEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_EbSoaEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbmxmUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_EbmxmkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_EbTPcUB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_Ebmxm0B8EfCiQ4wRMsUgSQ" elementId="title" name="Title"/>
    <parameters xmi:id="_EbmxnEB8EfCiQ4wRMsUgSQ" elementId="message" name="Message"/>
    <parameters xmi:id="_EbmxnUB8EfCiQ4wRMsUgSQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_EbmxnkB8EfCiQ4wRMsUgSQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_Ebmxn0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYgEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYgUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYgkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_EbTPd0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYg0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYhEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYhUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYhkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYh0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_EbSoYkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYiEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_EbTPcEB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYiUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_EbSoZUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYikB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_EbSobUB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYi0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYjEB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.manageembeddedsoftwarepackages" commandName="Manage Embedded Software Packages" description="Manage Embedded Software Packages" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYjUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_EbSoaEB8EfCiQ4wRMsUgSQ">
    <parameters xmi:id="_EbnYjkB8EfCiQ4wRMsUgSQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_EbnYj0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_EbSoY0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_EbnYkEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_EbSoa0B8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdRCwEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet/com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.action.TerminateAndRelaunch" commandName="Terminate And Relaunch" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdTfAEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.informationcenter.actionSet3/com.st.stm32cube.ide.mcu.informationcenter.action1" commandName="Information Center" description="Information Center" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdVUMEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdVUMUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdVUMkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdV7QEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdZloEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdaMsEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdazwEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdazwUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdazwkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdba0EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdcB4EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdcB4UB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdcB4kB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdcB40B8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdco8EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdco8UB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdeeIEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdeeIUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdfFMEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdfFMUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdfFMkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdfsQEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdfsQUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdgTUEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdgTUUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdgTUkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdg6YEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdg6YUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdhhcEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdiIgEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdivkEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdivkUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdivkkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdjWoEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdjWoUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdj9sEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdkkwEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdkkwUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdlL0EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdlL0UB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdly4EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdly4UB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdly4kB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdly40B8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdmZ8EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdmZ8UB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdmZ8kB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdnBAEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdnBAUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdnoEEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdnoEUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdoPIEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdoPIUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdoPIkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdo2MEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdo2MUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdo2MkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdpdQEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdpdQUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdpdQkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdqEUEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdqEUUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdqEUkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdqrYEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdqrYUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdrScEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdrScUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdrSckB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdr5gEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdr5gUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdr5gkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdsgkEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdsgkUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtHoEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtHoUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtHokB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtHo0B8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtusEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtusUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FdtuskB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FduVwEB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FduVwUB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_FduVwkB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdu80EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdu80UB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdu80kB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdu800B8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_Fdvj4EB8EfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_EbTPdkB8EfCiQ4wRMsUgSQ"/>
  <addons xmi:id="_EZ1P0EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_EZ1P0UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_EZ1P0kB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_EZ1P00B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_EZ1P1EB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_EZ1P1UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_EZ1P1kB8EfCiQ4wRMsUgSQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_EZ1P10B8EfCiQ4wRMsUgSQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_EZ1P2EB8EfCiQ4wRMsUgSQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_EZ1P2UB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_EaRUsEB8EfCiQ4wRMsUgSQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_EbSoYEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_EbSoYUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_EbSoYkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_EbSoY0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_EbSoZEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_EbSoZUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_EbSoZkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_EbSoZ0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_EbSoaEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_EbSoaUB8EfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_EbSoakB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_EbSoa0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_EbSobEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_EbSobUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_EbSobkB8EfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.launch.restartCategory" name="Restart Category"/>
  <categories xmi:id="_EbSob0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_EbSocEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_EbSocUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_EbSockB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_EbSoc0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_EbSodEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_EbSodUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_EbSodkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_EbSod0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_EbTPcEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_EbTPcUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_EbTPckB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_EbTPc0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_EbTPdEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_EbTPdUB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_EbTPdkB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_EbTPd0B8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_EbTPeEB8EfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
