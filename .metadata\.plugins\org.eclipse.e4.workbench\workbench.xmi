<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_8bmyMEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_8bpOcEDBEfCiQ4wRMsUgSQ" bindingContexts="_8bvVk0DBEfCiQ4wRMsUgSQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;C.ioc&quot; tooltip=&quot;C/C.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/C/C.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_8bpOcEDBEfCiQ4wRMsUgSQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_8bpOcUDBEfCiQ4wRMsUgSQ" x="156" y="156" width="1050" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_8bpOcUDBEfCiQ4wRMsUgSQ" selectedElement="_8bpOckDBEfCiQ4wRMsUgSQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_8bpOckDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_8bp1lkDBEfCiQ4wRMsUgSQ">
        <children xsi:type="advanced:Perspective" xmi:id="_8bpOc0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_8bp1gEDBEfCiQ4wRMsUgSQ" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1gEDBEfCiQ4wRMsUgSQ" selectedElement="_8bp1gUDBEfCiQ4wRMsUgSQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_8bp1gUDBEfCiQ4wRMsUgSQ" elementId="topLeft" containerData="2500" selectedElement="_8bp1gkDBEfCiQ4wRMsUgSQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_8bp1gkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_8bs5BUDBEfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_8bp1g0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_8bs5H0DBEfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_8bp1hEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_8bs5IEDBEfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1hUDBEfCiQ4wRMsUgSQ" containerData="7500" selectedElement="_8bp1jEDBEfCiQ4wRMsUgSQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1hkDBEfCiQ4wRMsUgSQ" containerData="7500" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_8bp1h0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_8bs5AkDBEfCiQ4wRMsUgSQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_8bp1iEDBEfCiQ4wRMsUgSQ" elementId="topRight" containerData="2500" selectedElement="_8bp1iUDBEfCiQ4wRMsUgSQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1iUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_8btf9EDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1ikDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_8btf90DBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1i0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_8btf-EDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1jEDBEfCiQ4wRMsUgSQ" containerData="2500" selectedElement="_8bp1jUDBEfCiQ4wRMsUgSQ" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_8bp1jUDBEfCiQ4wRMsUgSQ" elementId="bottom" containerData="5000" selectedElement="_8bp1jkDBEfCiQ4wRMsUgSQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1jkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" ref="_8bs5IUDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1j0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" ref="_8bs5MUDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1kEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_8bs5N0DBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1kUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" ref="_8btf50DBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_8bp1kkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack" containerData="5000" selectedElement="_8bp1k0DBEfCiQ4wRMsUgSQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1k0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" ref="_8btgCkDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1lEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" ref="_8btgFUDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1lUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" ref="_8btgGkDBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_8bp1lkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.perspective" selectedElement="_8bp1l0DBEfCiQ4wRMsUgSQ" label="Device Configuration Tool" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1l0DBEfCiQ4wRMsUgSQ" selectedElement="_8bp1mkDBEfCiQ4wRMsUgSQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_8bp1mEDBEfCiQ4wRMsUgSQ" elementId="left" containerData="1100" selectedElement="_8bp1mUDBEfCiQ4wRMsUgSQ">
              <tags>noFocus</tags>
              <children xsi:type="advanced:Placeholder" xmi:id="_8bp1mUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_8bs5BUDBEfCiQ4wRMsUgSQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
                <tags>active</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1mkDBEfCiQ4wRMsUgSQ" containerData="8900" selectedElement="_8bp1m0DBEfCiQ4wRMsUgSQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_8bp1m0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editorss" containerData="9000" ref="_8bs5AkDBEfCiQ4wRMsUgSQ"/>
              <children xsi:type="basic:PartSashContainer" xmi:id="_8bp1nEDBEfCiQ4wRMsUgSQ" toBeRendered="false" containerData="1000">
                <children xsi:type="basic:PartStack" xmi:id="_8bp1nUDBEfCiQ4wRMsUgSQ" elementId="topRight" toBeRendered="false" containerData="5000"/>
                <children xsi:type="basic:PartStack" xmi:id="_8bp1nkDBEfCiQ4wRMsUgSQ" elementId="bottomRight" toBeRendered="false" containerData="5000">
                  <children xsi:type="advanced:Placeholder" xmi:id="_8bp1n0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.views.OutputsView" toBeRendered="false" ref="_8btgH0DBEfCiQ4wRMsUgSQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Device Configuration Tool</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_8bp1oEDBEfCiQ4wRMsUgSQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_8bp1oUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_8bs4_UDBEfCiQ4wRMsUgSQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_8bp1okDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_8bs4_kDBEfCiQ4wRMsUgSQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_8bp1o0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_8bs5AUDBEfCiQ4wRMsUgSQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs4_UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs4_kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Information Center" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;file:///C:/ST/STM32CubeIDE_1.18.1/STM32CubeIDE/configuration/org.eclipse.osgi/73/0/.cp/welcome/index.html&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8bs4_0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8bs5AEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5AUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_8bs5AkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editorss" selectedElement="_8bs5A0DBEfCiQ4wRMsUgSQ">
      <children xsi:type="basic:PartStack" xmi:id="_8bs5A0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_8bs5BEDBEfCiQ4wRMsUgSQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_8bs5BEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="C.ioc" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;C.ioc&quot; partName=&quot;C.ioc&quot; title=&quot;C.ioc&quot; tooltip=&quot;Device Configuration Tool&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/C/C.ioc&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>com.st.stm32cube.common.mx.startCubeMx</tags>
          <tags>active</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5BUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8bs5BkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8bs5GkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5H0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5IEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5IUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8bs5IkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8bs5LkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5MUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.priorityField=&quot;30&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8bs5MkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8bs5NEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8bs5N0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8bs5OEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btf4EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btf50DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8btf6EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btf70DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btf9EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_8btf9UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btf9kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btf90DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btf-EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view horizontalPosition=&quot;0&quot; verticalPosition=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
      <menus xmi:id="_8btf-UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btgAUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btgCkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_8btgC0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btgEkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btgFUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_8btgFkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btgF0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btgGkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_8btgG0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_8btgHEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_8btgH0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.views.OutputsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
      <tags>View</tags>
      <tags>categoryTag:Device Configuration Tool</tags>
    </sharedElements>
    <trimBars xmi:id="_8btgIEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_8btgIUDBEfCiQ4wRMsUgSQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8btgIkDBEfCiQ4wRMsUgSQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8btgI0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_8btgK0DBEfCiQ4wRMsUgSQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_8b4fWEDBEfCiQ4wRMsUgSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8btgM0DBEfCiQ4wRMsUgSQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8btgNEDBEfCiQ4wRMsUgSQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8btgNUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_8btgN0DBEfCiQ4wRMsUgSQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_8b4fRkDBEfCiQ4wRMsUgSQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_8btgOEDBEfCiQ4wRMsUgSQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_8b4f6kDBEfCiQ4wRMsUgSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8btgPUDBEfCiQ4wRMsUgSQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8btgPkDBEfCiQ4wRMsUgSQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8btgP0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buG9UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHGkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHHkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHJkDBEfCiQ4wRMsUgSQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8buHJ0DBEfCiQ4wRMsUgSQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHKEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_8buHLkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_8b34jEDBEfCiQ4wRMsUgSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHNUDBEfCiQ4wRMsUgSQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8buHNkDBEfCiQ4wRMsUgSQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHN0DBEfCiQ4wRMsUgSQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_8buHOEDBEfCiQ4wRMsUgSQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_8buHOUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8buHPEDBEfCiQ4wRMsUgSQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8buHQEDBEfCiQ4wRMsUgSQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_8buHQUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_8buHQkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8buHR0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_8buHSEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_8buHTEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_8buHTUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_8buHTkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_8buHT0DBEfCiQ4wRMsUgSQ" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_8bvVk0DBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8buHUEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+1" command="_8b34hUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHUUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+L" command="_8b4fn0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHUkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SPACE" command="_8b4fLUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHU0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+V" command="_8b3Q7kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHVEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+SPACE" command="_8b4fF0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHVUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+A" command="_8b4fvEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHVkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+C" command="_8b2qAEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHV0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+X" command="_8b4fTkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHWEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+Y" command="_8b4f6kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHWUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+Z" command="_8b4fRkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHWkDBEfCiQ4wRMsUgSQ" keySequence="ALT+PAGE_UP" command="_8b4gAEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHW0DBEfCiQ4wRMsUgSQ" keySequence="ALT+PAGE_DOWN" command="_8b34RUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHXEDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+INSERT" command="_8b3Q7kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHXUDBEfCiQ4wRMsUgSQ" keySequence="ALT+F11" command="_8b34G0DBEfCiQ4wRMsUgSQ">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_8buHXkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F10" command="_8b33_UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHX0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+INSERT" command="_8b2qAEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHYEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+PAGE_UP" command="_8b4fc0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buHYUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+PAGE_DOWN" command="_8b34jUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuAEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+F3" command="_8b4fZUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuAUDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+DEL" command="_8b4fTkDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8buuAkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_8bvVmEDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8buuA0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+CR" command="_8b4fY0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuBEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+BS" command="_8b2p90DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuBUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+Q" command="_8b34ckDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuBkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+J" command="_8b34YkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuB0DBEfCiQ4wRMsUgSQ" keySequence="CTRL++" command="_8b34GUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuCEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+-" command="_8b4fokDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuCUDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+J" command="_8b34f0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuCkDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+A" command="_8b3Q40DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuC0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+J" command="_8b34A0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuDEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+L" command="_8b4fQUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuDUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+D" command="_8b34DUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuDkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+=" command="_8b34GUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuD0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Y" command="_8b2p70DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuEEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+DEL" command="_8b4fMkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuEUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+X" command="_8b2qC0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuEkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+Y" command="_8b4foUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuE0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+DEL" command="_8b4fQ0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuFEDBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_UP" command="_8b4f_EDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuFUDBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_DOWN" command="_8b34UUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuFkDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+END" command="_8b4fqEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuF0DBEfCiQ4wRMsUgSQ" keySequence="SHIFT+HOME" command="_8b4fjUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuGEDBEfCiQ4wRMsUgSQ" keySequence="END" command="_8b4ff0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuGUDBEfCiQ4wRMsUgSQ" keySequence="INSERT" command="_8b339UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuGkDBEfCiQ4wRMsUgSQ" keySequence="F2" command="_8b34j0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuG0DBEfCiQ4wRMsUgSQ" keySequence="HOME" command="_8b4fqkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuHEDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_UP" command="_8b4f10DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuHUDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_8b4fyUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuHkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+INSERT" command="_8b34SUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuH0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_8b4fq0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuIEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_8b34VEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuIUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F10" command="_8b4fX0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuIkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+END" command="_8b34V0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuI0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_UP" command="_8b34LUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuJEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_DOWN" command="_8b5GF0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuJUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_LEFT" command="_8b2p-EDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuJkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+ARROW_RIGHT" command="_8b34b0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuJ0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+HOME" command="_8b3Q7UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuKEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_8b34cUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuKUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_ADD" command="_8b4fxEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuKkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_8b4fYUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuK0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_8b34MEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuLEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_8b34fUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuLUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_8b339kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuLkDBEfCiQ4wRMsUgSQ" keySequence="ALT+/" command="_8b4frUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuL0DBEfCiQ4wRMsUgSQ" keySequence="SHIFT+CR" command="_8b4fqUDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8buuMEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_8bvVnEDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8buuMUDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+SHIFT+C" command="_8b5GGUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuMkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+TAB" command="_8b5GE0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuM0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+P" command="_8b4fgUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuNEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+T" command="_8b4fFkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuNUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+7" command="_8b3Q8UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuNkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+H" command="_8b34BkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuN0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+N" command="_8b34bkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuOEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+/" command="_8b3Q8UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuOUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+O" command="_8b4fXkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuOkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+A" command="_8b4fZkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuO0DBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+S" command="_8b4f8EDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuPEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+#" command="_8b4fC0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuPUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+C" command="_8b3Q8UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuPkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F" command="_8b5GFEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuP0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+G" command="_8b2p8kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuQEDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+H" command="_8b34iEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuQUDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+I" command="_8b3Q9UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuQkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+T" command="_8b34W0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuQ0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+I" command="_8b34YEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuREDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+/" command="_8b4fw0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuRUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+O" command="_8b2qE0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuRkDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+R" command="_8b4fN0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuR0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+S" command="_8b33_kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuSEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+T" command="_8b4fDkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuSUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_8b4f5UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuSkDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+L" command="_8b4fy0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuS0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+M" command="_8b3Q5EDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuTEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+=" command="_8b4fC0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuTUDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+O" command="_8b4fUEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuTkDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Z" command="_8b4fUUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuT0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+\" command="_8b3Q8kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuUEDBEfCiQ4wRMsUgSQ" keySequence="F3" command="_8b5GHUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuUUDBEfCiQ4wRMsUgSQ" keySequence="F4" command="_8b4fvUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuUkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_8b4fO0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuU0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_8b34R0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuVEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_UP" command="_8b4fkEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuVUDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_8b5GFUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuVkDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_8b4fLkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuV0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_8b4fl0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuWEDBEfCiQ4wRMsUgSQ" keySequence="ALT+C" command="_8b3Q80DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuWUDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+TAB" command="_8b34CEDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8buuWkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_8bvVlEDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8buuW0DBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+SHIFT+L" command="_8b3Q-0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuXEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q O" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuXUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_8buuXkDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+B" command="_8b34PEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuX0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+R" command="_8b5GG0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuYEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q Q" command="_8b34NEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuYUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+S" command="_8b34EkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuYkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+3" command="_8b34jkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuY0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q S" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuZEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_8buuZUDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q V" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuZkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_8buuZ0DBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+G" command="_8b34JkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuaEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+W" command="_8b4fTUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuaUDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q H" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuakDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_8buua0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+K" command="_8b34J0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buubEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+," command="_8b3Q9EDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buubUDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q L" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buubkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_8buub0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+." command="_8b4f2kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buucEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+B" command="_8b34KEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buucUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+E" command="_8b34Q0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuckDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q X" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuc0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_8buudEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q Y" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buudUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_8buudkDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q Z" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buud0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_8buueEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+P" command="_8b4fWEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buueUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+Q" command="_8b4fbUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuekDBEfCiQ4wRMsUgSQ" keySequence="CTRL+S" command="_8b4fo0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buue0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+W" command="_8b4f9UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buufEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+H" command="_8b4fK0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buufUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+K" command="_8b34PkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buufkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+M" command="_8b4fJEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuf0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+N" command="_8b4f8UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buugEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+B" command="_8b3Q-EDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buugUDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q B" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buugkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_8buug0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+Q C" command="_8b34NEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuhEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_8buuhUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+E" command="_8b4fP0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuhkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F" command="_8b34GEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuh0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+W" command="_8b4f1kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuiEDBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+N" command="_8b4fSUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuiUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+_" command="_8b4fHEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buuikDBEfCiQ4wRMsUgSQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_8buui0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+{" command="_8b4fHEDBEfCiQ4wRMsUgSQ">
      <parameters xmi:id="_8buujEDBEfCiQ4wRMsUgSQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_8buujUDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+F9" command="_8b4fW0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buujkDBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_LEFT" command="_8b34AUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuj0DBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_RIGHT" command="_8b4fZEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buukEDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+F5" command="_8b4f0UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buukUDBEfCiQ4wRMsUgSQ" keySequence="ALT+F7" command="_8b3Q70DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buukkDBEfCiQ4wRMsUgSQ" keySequence="F9" command="_8b4fhUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buuk0DBEfCiQ4wRMsUgSQ" keySequence="F11" command="_8b4fuUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buulEDBEfCiQ4wRMsUgSQ" keySequence="F12" command="_8b4fMEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8buulUDBEfCiQ4wRMsUgSQ" keySequence="F2" command="_8b3Q9kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVEEDBEfCiQ4wRMsUgSQ" keySequence="F5" command="_8b4fgEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVEUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F7" command="_8b4fukDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVEkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F8" command="_8b4fGkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVE0DBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_8b4fbUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVFEDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_8b34MkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVFUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F4" command="_8b4fTUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVFkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+F6" command="_8b34F0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVF0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+F7" command="_8b2qAUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVGEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F8" command="_8b34h0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVGUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F4" command="_8b4f9UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVGkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F6" command="_8b34H0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVG0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+F7" command="_8b34WkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVHEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_8b34EUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVHUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_8b4fH0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVHkDBEfCiQ4wRMsUgSQ" keySequence="DEL" command="_8b34JEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVH0DBEfCiQ4wRMsUgSQ" keySequence="ALT+-" command="_8b2qFkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVIEDBEfCiQ4wRMsUgSQ" keySequence="ALT+CR" command="_8b4fDEDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVIUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_8bvVmkDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVIkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+P" command="_8b4flEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVI0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+G" command="_8b4ffkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVJEDBEfCiQ4wRMsUgSQ" keySequence="F3" command="_8b4fbkDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVJUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_8bv8KUDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVJkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+T" command="_8b4fFkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVJ0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+H" command="_8b34BkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVKEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+G" command="_8b2p8kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVKUDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+H" command="_8b34iEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVKkDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+I" command="_8b3Q9UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVK0DBEfCiQ4wRMsUgSQ" keySequence="ALT+SHIFT+R" command="_8b4fN0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVLEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_8b4f5UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVLUDBEfCiQ4wRMsUgSQ" keySequence="F3" command="_8b5GHUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVLkDBEfCiQ4wRMsUgSQ" keySequence="F4" command="_8b4fvUDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVL0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_8bvVlkDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVMEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+V" command="_8b4fk0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVMUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+C" command="_8b4fA0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVMkDBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_UP" command="_8b2p9kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVM0DBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_RIGHT" command="_8b4ftkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVNEDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+INSERT" command="_8b4fk0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVNUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+INSERT" command="_8b4fA0DBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVNkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_8bvVm0DBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVN0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+/" command="_8b34OUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVOEDBEfCiQ4wRMsUgSQ" keySequence="F3" command="_8b34C0DBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVOUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_8bv8IUDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVOkDBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+M" command="_8b4fu0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVO0DBEfCiQ4wRMsUgSQ" keySequence="ALT+CTRL+N" command="_8b4fyEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVPEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+T" command="_8b4fBkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVPUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+W" command="_8b33-0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVPkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+N" command="_8b34HEDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVP0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_8bv8IkDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVQEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+R" command="_8b2qB0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVQUDBEfCiQ4wRMsUgSQ" keySequence="F7" command="_8b4f4UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVQkDBEfCiQ4wRMsUgSQ" keySequence="F8" command="_8b338UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVQ0DBEfCiQ4wRMsUgSQ" keySequence="F5" command="_8b338kDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVREDBEfCiQ4wRMsUgSQ" keySequence="F6" command="_8b4fsUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVRUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F2" command="_8b4fM0DBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVRkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_8bv8I0DBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVR0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+," command="_8b4fb0DBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVSEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+SHIFT+." command="_8b4fIUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVSUDBEfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_8b4fIkDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVSkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_8bv8JkDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVS0DBEfCiQ4wRMsUgSQ" keySequence="CTRL+G" command="_8b4f7UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVTEDBEfCiQ4wRMsUgSQ" keySequence="HOME" command="_8b4fAUDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVTUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.console" bindingContext="_8bvVnUDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVTkDBEfCiQ4wRMsUgSQ" keySequence="CTRL+Z" command="_8b4f1EDBEfCiQ4wRMsUgSQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_8bvVT0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_8bv8J0DBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVUEDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+F7" command="_8b34CkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVUUDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+F8" command="_8b4fGUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVUkDBEfCiQ4wRMsUgSQ" keySequence="SHIFT+F5" command="_8b4fskDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVU0DBEfCiQ4wRMsUgSQ" keySequence="SHIFT+F6" command="_8b3Q-UDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVVEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+F5" command="_8b4frkDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVVUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_8bv8K0DBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVVkDBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_LEFT" command="_8b34XUDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVV0DBEfCiQ4wRMsUgSQ" keySequence="ALT+ARROW_RIGHT" command="_8b34eEDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVWEDBEfCiQ4wRMsUgSQ" keySequence="F3" command="_8b5GHUDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVWUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_8bvVmUDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVWkDBEfCiQ4wRMsUgSQ" keySequence="F3" command="_8b5GHUDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVW0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_8bv8IEDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVXEDBEfCiQ4wRMsUgSQ" keySequence="CTRL+INSERT" command="_8b34EEDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVXUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_8bv8KEDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVXkDBEfCiQ4wRMsUgSQ" keySequence="ALT+Y" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVX0DBEfCiQ4wRMsUgSQ" keySequence="ALT+A" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVYEDBEfCiQ4wRMsUgSQ" keySequence="ALT+B" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVYUDBEfCiQ4wRMsUgSQ" keySequence="ALT+C" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVYkDBEfCiQ4wRMsUgSQ" keySequence="ALT+D" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVY0DBEfCiQ4wRMsUgSQ" keySequence="ALT+E" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVZEDBEfCiQ4wRMsUgSQ" keySequence="ALT+F" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVZUDBEfCiQ4wRMsUgSQ" keySequence="ALT+G" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVZkDBEfCiQ4wRMsUgSQ" keySequence="ALT+P" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVZ0DBEfCiQ4wRMsUgSQ" keySequence="ALT+R" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVaEDBEfCiQ4wRMsUgSQ" keySequence="ALT+S" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVaUDBEfCiQ4wRMsUgSQ" keySequence="ALT+T" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVakDBEfCiQ4wRMsUgSQ" keySequence="ALT+V" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVa0DBEfCiQ4wRMsUgSQ" keySequence="ALT+W" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVbEDBEfCiQ4wRMsUgSQ" keySequence="ALT+H" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVbUDBEfCiQ4wRMsUgSQ" keySequence="ALT+L" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
    <bindings xmi:id="_8bvVbkDBEfCiQ4wRMsUgSQ" keySequence="ALT+N" command="_8b2qEkDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVb0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.context" bindingContext="_8bv8LkDBEfCiQ4wRMsUgSQ">
    <bindings xmi:id="_8bvVcEDBEfCiQ4wRMsUgSQ" keySequence="ALT+K" command="_8b34TEDBEfCiQ4wRMsUgSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_8bvVcUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8L0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVckDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8MEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVc0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8MUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVdEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8MkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVdUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8M0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVdkDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8NEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVd0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8NUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVeEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8NkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVeUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8N0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVekDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8OEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVe0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8OUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVfEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8OkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVfUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8O0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVfkDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8PEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVf0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8PUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVgEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8PkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVgUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8P0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVgkDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8QEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVg0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8QUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVhEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8QkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVhUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8Q0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVhkDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8REDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVh0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8RUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvViEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8RkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvViUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8R0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVikDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8SEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVi0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8SUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVjEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8SkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVjUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8S0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVjkDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8TEDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVj0DBEfCiQ4wRMsUgSQ" bindingContext="_8bv8TUDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVkEDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8TkDBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVkUDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8T0DBEfCiQ4wRMsUgSQ"/>
  <bindingTables xmi:id="_8bvVkkDBEfCiQ4wRMsUgSQ" bindingContext="_8bv8UEDBEfCiQ4wRMsUgSQ"/>
  <rootContext xmi:id="_8bvVk0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_8bvVlEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_8bvVlUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_8bvVlkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_8bvVl0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_8bvVmEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_8bvVmUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_8bvVmkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_8bvVm0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_8bvVnEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_8bvVnUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_8bvVnkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_8bv8IEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_8bv8IUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_8bv8IkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_8bv8I0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_8bv8JEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mpu.debug.ui.debugging" name="Debugging C/C++ on MPU - Cortex-M" description="Debugging C/C++ Programs on MPU - Cortex-M"/>
        <children xmi:id="_8bv8JUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.ui.debugging" name="Debugging C/C++ on MCU" description="Debugging C/C++ Programs on MCU"/>
        <children xmi:id="_8bv8JkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_8bv8J0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_8bv8KEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_8bv8KUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
    </children>
    <children xmi:id="_8bv8KkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_8bv8K0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_8bv8LEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_8bv8LUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_8bv8LkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.context" name="Device Configuration Tool Context" description="Device Configuration Tool  Context"/>
  <rootContext xmi:id="_8bv8L0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet" name="Auto::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet"/>
  <rootContext xmi:id="_8bv8MEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.informationcenter.actionSet3" name="Auto::com.st.stm32cube.ide.mcu.informationcenter.actionSet3"/>
  <rootContext xmi:id="_8bv8MUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_8bv8MkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_8bv8M0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_8bv8NEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_8bv8NUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_8bv8NkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_8bv8N0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_8bv8OEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_8bv8OUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_8bv8OkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_8bv8O0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_8bv8PEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_8bv8PUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_8bv8PkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_8bv8P0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_8bv8QEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_8bv8QUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_8bv8QkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_8bv8Q0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_8bv8REDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_8bv8RUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_8bv8RkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_8bv8R0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_8bv8SEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_8bv8SUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_8bv8SkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_8bv8S0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_8bv8TEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_8bv8TUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_8bv8TkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_8bv8T0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_8bv8UEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_8bv8UUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8UkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.views.OutputsView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" category="Device Configuration Tool" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
    <tags>View</tags>
    <tags>categoryTag:Device Configuration Tool</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8U0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8VEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8VUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.logview" label="SWV Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.SWVLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8VkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.statisticalprofiling" label="SWV Statistical Profiling" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_statistical_profiling.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.statisticalprofiling.SWVStatisticalProfilingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8V0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.itmtrace" label="SWV ITM Data Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/console_view.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.itmtrace.SWVConsole"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8WEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.exceptionlogview" label="SWV Exception Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_Exception_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.SWVExceptionLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8WUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceview" label="SWV Data Trace" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/insp_sbook.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatrace.SWVDataTraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8WkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.SWVDatatraceTimeline" label="SWV Data Trace Timeline Graph" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/Datatrace_timeline.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceTimeline.SWVDatatraceTimeline"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8W0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.faultanalyzer.view" label="Fault Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.faultanalyzer/icons/clanbomber.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.faultanalyzer.FaultAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.faultanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8XEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.queues" label="FreeRTOS Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.queues.FORtosQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8XUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.tasklist" label="FreeRTOS Task List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.tasklist.FORtosTaskList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8XkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.semaphore" label="FreeRTOS Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.semaphores.FORtosSemaphores"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8X0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.freertos.timers" label="FreeRTOS Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.timers.FORtosTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8YEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" label="Live Expressions" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8YUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.sfrview" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8YkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8Y0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.tcp.console.view" label="TCP Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.tcp.console/icons/console.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.tcp.console.ui.TCPConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.tcp.console"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8ZEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.threads" label="ThreadX Thread List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.threadlist.ThreadXThreadList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8ZUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.semaphores" label="ThreadX Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.semaphores.ThreadXSemaphoreList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8ZkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.mutexes" label="ThreadX Mutexes" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.mutexes.ThreadXMutexList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8Z0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.queues" label="ThreadX Message Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.queues.ThreadXMessageQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8aEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.eventflags" label="ThreadX Event Flags" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.eventflags.ThreadXEventFlags"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8aUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.timer" label="ThreadX Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.timers.ThreadXTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8akDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.blockpools" label="ThreadX Memory Block Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.blockpools.ThreadXMemoryBlockPools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8a0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.bytepools" label="ThreadX Memory Byte Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.bytepools.ThreadXMemoryBytePools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8bEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8bUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8bkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8b0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8cEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8cUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8ckDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8c0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8dEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8dUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8dkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8d0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8eEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8eUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8ekDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8e0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8fEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8fUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8fkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8f0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8gEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8gUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8gkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8g0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8hEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8hUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8hkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8h0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8iEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8iUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8ikDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8i0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8jEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8jUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8jkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8j0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8kEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8kUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8kkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8k0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8lEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8lUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_8bv8lkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_8b2p3UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_8b2p3kDBEfCiQ4wRMsUgSQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8b2p30DBEfCiQ4wRMsUgSQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8b2p4EDBEfCiQ4wRMsUgSQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_8b2p7kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p70DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p8EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p8UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p8kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p80DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_8b5tJ0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p9EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p9UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p9kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_8b5tJUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p90DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p-EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p-UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_8b5tJEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p-kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p-0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_8b5Gk0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b2p_EDBEfCiQ4wRMsUgSQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_8b2p_UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2p_kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b2p_0DBEfCiQ4wRMsUgSQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_8b2qAEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qAUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qAkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_8b5GiEDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b2qA0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_8b2qBEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qBUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qBkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qB0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qCEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_8b5tK0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qCUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qCkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qC0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qDEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qDUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qDkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qD0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qEEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qEUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qEkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_8b5tJUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qE0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qFEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_8b5tJEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qFUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qFkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qF0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qGEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qGUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_8b5tLUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b2qGkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_8b5tMEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q4EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q4UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q4kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q40DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q5EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q5UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q5kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q50DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q6EDBEfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_8b5Gj0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q6UDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mpu.linux.ide.command.setupopenstlinux" commandName="Setup OpenSTLinux" description="Setup OpenSTLinux" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q6kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q60DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q7EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q7UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q7kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q70DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q8EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q8UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q8kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q80DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q9EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q9UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q9kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q90DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q-EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q-UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_8b5tLkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q-kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q-0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_8b5GkkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q_EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q_UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_8b5GjEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q_kDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.ide.command.generatecode" commandName="Generate Code" description="Generate Code (based on .ioc file content)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3Q_0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3RAEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3RAUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3RAkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_8b5tLEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b338EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_8b5tMUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b338UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b338kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3380DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b339EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b339UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b339kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b3390DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b33-EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b33-UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b33-kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_8b33-0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b33_EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b33_UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b33_kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b33_0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34AEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34AUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34AkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34A0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34BEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34BUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34BkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34B0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34CEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34CUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34CkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_8b5tLkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34C0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_8b5tJkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34DEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34DUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34DkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_8b5tK0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34D0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartConfigurationCommand" commandName="Restart Configuration Command" category="_8b5GlEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34EEDBEfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_8b5Gj0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34EUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34EkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34E0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.externaltools.test" commandName="Test ExternalTools" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34FEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34FUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34FkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34F0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34GEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34GUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34GkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34G0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34HEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34HUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34HkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_8b5GhkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34H0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34IEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34IUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34IkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.common.ui.view_export" commandName="Export view data to file" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34I0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34JEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34JUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34JkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_8b5tJ0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34J0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34KEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34KUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_8b5tKEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34KkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34K0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_8b5tLkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34LEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_8b34LUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34LkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34L0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34MEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34MUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34MkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34M0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34NEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_8b5Gh0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34NUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_8b34NkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_8b34N0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_8b34OEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34OUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_8b5tJkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34OkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_8b5GjkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34O0DBEfCiQ4wRMsUgSQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_8b34PEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34PUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34PkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34P0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34QEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34QUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_8b5GkEDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34QkDBEfCiQ4wRMsUgSQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_8b34Q0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34REDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34RUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34RkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34R0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34SEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34SUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34SkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_8b5tIUDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34S0DBEfCiQ4wRMsUgSQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_8b34TEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.menu.generatecode" commandName="Generate Code" description="Generate Code" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34TUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34TkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34T0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34UEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34UUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34UkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.datarefresh" commandName="Data Refresh" description="Data Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34U0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34VEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34VUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34VkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34V0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34WEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_8b5tKEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34WUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34WkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34W0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34XEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showInCommand" commandName="name" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34XUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34XkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34X0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34YEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34YUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34YkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34Y0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34ZEDBEfCiQ4wRMsUgSQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_8b34ZUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34ZkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34Z0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_8b5tJEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34aEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_8b5GjEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34aUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_8b5GjUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34akDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34a0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_8b5tJ0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34bEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34bUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_8b5tJ0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34bkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34b0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34cEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34cUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34ckDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34c0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34dEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showstate" commandName="name" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34dUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_8b34dkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34d0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34eEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34eUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34ekDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.docsandresources" commandName="Docs And Resources" description="Docs And Resources" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34e0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_8b5tIUDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b34fEDBEfCiQ4wRMsUgSQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_8b34fUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34fkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34f0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34gEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34gUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34gkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34g0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_8b5GjEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34hEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34hUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34hkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_8b5tKEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34h0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34iEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34iUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34ikDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.generatereport" commandName="Generate Report" description="Generate Report" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34i0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34jEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34jUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34jkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b34j0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fAEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fAUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fAkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fA0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_8b5tJUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fBEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fBUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fBkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fB0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fCEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.informationcenter.tutorialvideo" commandName="Tutorial Video" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fCUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_8b5tLEDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fCkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_8b4fC0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fDEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fDUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fDkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fD0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_8b5Gk0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fEEDBEfCiQ4wRMsUgSQ" elementId="url" name="URL"/>
    <parameters xmi:id="_8b4fEUDBEfCiQ4wRMsUgSQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_8b4fEkDBEfCiQ4wRMsUgSQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_8b4fE0DBEfCiQ4wRMsUgSQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_8b4fFEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fFUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fFkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fF0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fGEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fGUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_8b5tLkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fGkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fG0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.toolbar.generatecode" commandName="Generate Code" description="Generate Code" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fHEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_8b5Gk0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fHUDBEfCiQ4wRMsUgSQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_8b4fHkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fH0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fIEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fIUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fIkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fI0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fJEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fJUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fJkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fJ0DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.start_trace" commandName="Start Trace" description="Start Trace" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fKEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fKUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fKkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fK0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_8b5tJ0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fLEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.modifyIncludePathsBySelection" commandName="Add/remove include path..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fLUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fLkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fL0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fMEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fMUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fMkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fM0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fNEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fNUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fNkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fN0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fOEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_8b5tMUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fOUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fOkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fO0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fPEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_8b5tK0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fPUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_8b4fPkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_8b4fP0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fQEDBEfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_8b5Gj0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fQUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fQkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fQ0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fREDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fRUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fRkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fR0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fSEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fSUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fSkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fS0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fTEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fTUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fTkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fT0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fUEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fUUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fUkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fU0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fVEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fVUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_8b5GkEDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fVkDBEfCiQ4wRMsUgSQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_8b4fV0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_8b5tLEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fWEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fWUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp" commandName="Convert Project to C or CPP" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fWkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp.type" name="Convert Type (C/C++)"/>
  </commands>
  <commands xmi:id="_8b4fW0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fXEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fXUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fXkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fX0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fYEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fYUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fYkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fY0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fZEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fZUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fZkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fZ0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4faEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.ide.connectionToMyST" commandName="Connection to myST" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4faUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mpu.remote.serial.connectconsole" commandName="Connect Console Command" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fakDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_8b5tJ0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fa0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fbEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fbUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fbkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fb0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fcEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fcUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_8b5tKEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fckDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fc0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fdEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_8b5tKkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fdUDBEfCiQ4wRMsUgSQ" elementId="title" name="Title"/>
    <parameters xmi:id="_8b4fdkDBEfCiQ4wRMsUgSQ" elementId="message" name="Message"/>
    <parameters xmi:id="_8b4fd0DBEfCiQ4wRMsUgSQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_8b4feEDBEfCiQ4wRMsUgSQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_8b4feUDBEfCiQ4wRMsUgSQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_8b4fekDBEfCiQ4wRMsUgSQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_8b4fe0DBEfCiQ4wRMsUgSQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_8b4ffEDBEfCiQ4wRMsUgSQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_8b4ffUDBEfCiQ4wRMsUgSQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_8b4ffkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4ff0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fgEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fgUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fgkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fg0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fhEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fhUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fhkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fh0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fiEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fiUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fikDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fi0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_8b5tMUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fjEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_8b5tJEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fjUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fjkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fj0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fkEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fkUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fkkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartCommand" commandName="Restart Command" category="_8b5GlEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fk0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_8b5tJUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4flEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4flUDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.checkforupdates" commandName="Check For Embedded Software Packages Updates" description="Check For Embedded Software Packages Updates" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4flkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fl0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fmEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_8b5GjkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fmUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_8b4fmkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fm0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fnEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fnUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_8b4fnkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_8b4fn0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4foEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4foUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fokDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fo0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fpEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_8b5tMUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fpUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_8b5GhkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fpkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_8b5tMEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fp0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fqEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fqUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fqkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fq0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4frEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4frUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4frkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fr0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fsEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_8b5GhkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fsUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fskDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_8b5tLkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fs0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4ftEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.threadx.trx_to_file_command" commandName="Export trace" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4ftUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4ftkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_8b5tJUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4ft0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_8b5GkEDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4fuEDBEfCiQ4wRMsUgSQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_8b4fuUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fukDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fu0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fvEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fvUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fvkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fv0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_8b5GjEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fwEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fwUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fwkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.cmake.cmake_run_builder" commandName="cmake_run_builder" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fw0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fxEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fxUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fxkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.openconfig" commandName="Config" description="Configure SWV" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fx0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_8b5tLUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fyEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fyUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fykDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_8b5tIkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fy0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fzEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fzUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fzkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4fz0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f0EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f0UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f0kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f00DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f1EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f1UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_8b5tMUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f1kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f10DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f2EDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.stlink.fwupgrade" commandName="ST-LINK Upgrade" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f2UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f2kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f20DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_8b5Gk0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4f3EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_8b4f3UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_8b5tIUDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4f3kDBEfCiQ4wRMsUgSQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_8b4f30DBEfCiQ4wRMsUgSQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_8b4f4EDBEfCiQ4wRMsUgSQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_8b4f4UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f4kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_8b5Gk0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4f40DBEfCiQ4wRMsUgSQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_8b4f5EDBEfCiQ4wRMsUgSQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_8b4f5UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f5kDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.cmake.commands.cmakeimport" commandName="cmakeimport" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f50DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_8b5tIUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f6EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_8b5tLUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f6UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f6kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f60DBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.p2.list" commandName="P2 IU List" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f7EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_8b5tIEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f7UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f7kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f70DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_8b5tI0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f8EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f8UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_8b5GkEDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4f8kDBEfCiQ4wRMsUgSQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_8b4f80DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f9EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f9UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_8b5GkEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f9kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f90DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f-EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f-UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f-kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f-0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f_EDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4f_UDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_8b5tL0DBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4f_kDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_8b4f_0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_8b4gAEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_8b5GjkDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4gAUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_8b5tIUDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4gAkDBEfCiQ4wRMsUgSQ" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_8b4gA0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_8b5GjkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4gBEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_8b4gBUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_8b5tKkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b4gBkDBEfCiQ4wRMsUgSQ" elementId="title" name="Title"/>
    <parameters xmi:id="_8b4gB0DBEfCiQ4wRMsUgSQ" elementId="message" name="Message"/>
    <parameters xmi:id="_8b4gCEDBEfCiQ4wRMsUgSQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_8b4gCUDBEfCiQ4wRMsUgSQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_8b4gCkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4gC0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b4gDEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GEEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_8b5tMEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GEUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GEkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GE0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GFEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GFUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_8b5GiEDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GFkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_8b5tKUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GF0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_8b5Gi0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GGEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_8b5Gk0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GGUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GGkDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.common.mx.manageembeddedsoftwarepackages" commandName="Manage Embedded Software Packages" description="Manage Embedded Software Packages" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GG0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_8b5GjkDBEfCiQ4wRMsUgSQ">
    <parameters xmi:id="_8b5GHEDBEfCiQ4wRMsUgSQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_8b5GHUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_8b5GiUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GHkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_8b5GkUDBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GH0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet/com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.action.TerminateAndRelaunch" commandName="Terminate And Relaunch" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GIEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.informationcenter.actionSet3/com.st.stm32cube.ide.mcu.informationcenter.action1" commandName="Information Center" description="Information Center" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GIUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GIkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GI0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GJEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GJUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GJkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GJ0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GKEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GKUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GKkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GK0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GLEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GLUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GLkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GL0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GMEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GMUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GMkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GM0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GNEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GNUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GNkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GN0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GOEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GOUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GOkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GO0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GPEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GPUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GPkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GP0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GQEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GQUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GQkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GQ0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GREDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GRUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GRkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GR0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GSEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GSUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GSkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GS0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GTEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GTUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GTkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GT0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GUEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GUUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GUkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GU0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GVEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GVUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GVkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GV0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GWEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GWUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GWkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GW0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GXEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GXUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GXkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GX0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GYEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GYUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GYkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GY0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GZEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GZUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GZkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GZ0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GaEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GaUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GakDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5Ga0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GbEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GbUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GbkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5Gb0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GcEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GcUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GckDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5Gc0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GdEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GdUDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GdkDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5Gd0DBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <commands xmi:id="_8b5GeEDBEfCiQ4wRMsUgSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_8b5tL0DBEfCiQ4wRMsUgSQ"/>
  <addons xmi:id="_8b5GeUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_8b5GekDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_8b5Ge0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_8b5GfEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_8b5GfUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_8b5GfkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_8b5Gf0DBEfCiQ4wRMsUgSQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_8b5GgEDBEfCiQ4wRMsUgSQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_8b5GgUDBEfCiQ4wRMsUgSQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_8b5GgkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_8b5Gg0DBEfCiQ4wRMsUgSQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_8b5GhEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_8b5GhUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_8b5GhkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_8b5Gh0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_8b5GiEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_8b5GiUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_8b5GikDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_8b5Gi0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_8b5GjEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_8b5GjUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_8b5GjkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_8b5Gj0DBEfCiQ4wRMsUgSQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_8b5GkEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_8b5GkUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_8b5GkkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_8b5Gk0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_8b5GlEDBEfCiQ4wRMsUgSQ" elementId="com.st.stm32cube.ide.mcu.debug.launch.restartCategory" name="Restart Category"/>
  <categories xmi:id="_8b5tIEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_8b5tIUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_8b5tIkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_8b5tI0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_8b5tJEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_8b5tJUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_8b5tJkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_8b5tJ0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_8b5tKEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_8b5tKUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_8b5tKkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_8b5tK0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_8b5tLEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_8b5tLUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_8b5tLkDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_8b5tL0DBEfCiQ4wRMsUgSQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_8b5tMEDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_8b5tMUDBEfCiQ4wRMsUgSQ" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
