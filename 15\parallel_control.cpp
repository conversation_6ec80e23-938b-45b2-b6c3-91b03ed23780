/*
 * CORDON-82 v15.5.0 PARALLEL EDITION
 * РЕАЛИЗАЦИЯ СИСТЕМЫ ПАРАЛЛЕЛЬНОГО УПРАВЛЕНИЯ
 * 
 * Неблокирующее управление моторами для максимального ускорения
 * ЦЕЛЬ: Сократить время цикла с 39 до 15 секунд!
 */

#include "parallel_control.h"
#include "motor_control.h"
#include "sensor_manager.h"
#include "config.h"

// ===== ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ =====
MotorState_t motor_states[8];           // Состояния моторов M1-M7 (индекс 0 не используется)
ParallelOperation_t current_operation;  // Текущая параллельная операция

static bool parallel_initialized = false;
static uint32_t last_update_time = 0;

// ===== ИНИЦИАЛИЗАЦИЯ =====
void initializeParallelControl() {
    Serial.println("Initializing parallel control system...");
    
    // Инициализация состояний моторов
    for (int i = 0; i < 8; i++) {
        motor_states[i].active = false;
        motor_states[i].completed = true;
        motor_states[i].motor_number = i;
        motor_states[i].direction = 0;
        motor_states[i].steps_total = 0;
        motor_states[i].steps_remaining = 0;
        motor_states[i].pulse_time = 1;
        motor_states[i].pause_time = 1;
        motor_states[i].use_microseconds = false;
        motor_states[i].last_step_time = 0;
        motor_states[i].step_phase = 0;
        motor_states[i].sensor_target = false;
        motor_states[i].target_sensor_num = 0;
    }
    
    // Инициализация параллельной операции
    current_operation.active = false;
    current_operation.completed = true;
    current_operation.motors_count = 0;
    current_operation.motors_completed = 0;
    current_operation.start_time = 0;
    current_operation.timeout_ms = 10000;
    strcpy(current_operation.operation_name, "NONE");
    
    parallel_initialized = true;
    Serial.println("Parallel control system initialized OK");
}

// ===== ОСНОВНЫЕ ФУНКЦИИ =====

bool startMotorMovement(int motor, int direction, uint16_t steps, uint16_t pulse_time, uint16_t pause_time, bool use_microseconds) {
    if (motor < 1 || motor > 6) {
        Serial.print("ERROR: Invalid motor number for parallel: ");
        Serial.println(motor);
        return false;
    }
    
    if (!parallel_initialized) {
        Serial.println("ERROR: Parallel control not initialized!");
        return false;
    }
    
    // Настройка состояния мотора
    motor_states[motor].active = true;
    motor_states[motor].completed = false;
    motor_states[motor].direction = direction;
    motor_states[motor].steps_total = steps;
    motor_states[motor].steps_remaining = steps;
    motor_states[motor].pulse_time = pulse_time;
    motor_states[motor].pause_time = pause_time;
    motor_states[motor].use_microseconds = use_microseconds;
    motor_states[motor].last_step_time = micros();
    motor_states[motor].step_phase = 0;
    motor_states[motor].sensor_target = false;
    
    // Выбор и настройка мотора
    selectMotor(motor);
    enableMotorDriver();
    digitalWrite(M1_DIR, direction ? HIGH : LOW);
    
    Serial.print("Started parallel movement: M");
    Serial.print(motor);
    Serial.print(" dir=");
    Serial.print(direction);
    Serial.print(" steps=");
    Serial.print(steps);
    Serial.print(" timing=");
    Serial.print(pulse_time);
    Serial.print(use_microseconds ? "μS+" : "ms+");
    Serial.print(pause_time);
    Serial.println(use_microseconds ? "μS" : "ms");
    
    return true;
}

bool startMotorToSensor(int motor, int direction, uint8_t target_sensor, uint16_t pulse_time, uint16_t pause_time, bool use_microseconds) {
    if (motor < 1 || motor > 6) {
        Serial.print("ERROR: Invalid motor number for sensor movement: ");
        Serial.println(motor);
        return false;
    }
    
    if (target_sensor < 1 || target_sensor > 14) {
        Serial.print("ERROR: Invalid target sensor: ");
        Serial.println(target_sensor);
        return false;
    }
    
    // Настройка состояния мотора для движения до датчика
    motor_states[motor].active = true;
    motor_states[motor].completed = false;
    motor_states[motor].direction = direction;
    motor_states[motor].steps_total = 9999; // Максимальное количество шагов
    motor_states[motor].steps_remaining = 9999;
    motor_states[motor].pulse_time = pulse_time;
    motor_states[motor].pause_time = pause_time;
    motor_states[motor].use_microseconds = use_microseconds;
    motor_states[motor].last_step_time = micros();
    motor_states[motor].step_phase = 0;
    motor_states[motor].sensor_target = true;
    motor_states[motor].target_sensor_num = target_sensor;
    
    // Выбор и настройка мотора
    selectMotor(motor);
    enableMotorDriver();
    digitalWrite(M1_DIR, direction ? HIGH : LOW);
    
    Serial.print("Started parallel movement to sensor: M");
    Serial.print(motor);
    Serial.print(" → D");
    Serial.print(target_sensor);
    Serial.print(" dir=");
    Serial.println(direction);
    
    return true;
}

void stopMotorMovement(int motor) {
    if (motor < 1 || motor > 6) return;
    
    motor_states[motor].active = false;
    motor_states[motor].completed = true;
    motor_states[motor].steps_remaining = 0;
    
    Serial.print("Stopped parallel movement: M");
    Serial.println(motor);
}

bool isMotorCompleted(int motor) {
    if (motor < 1 || motor > 6) return true;
    return motor_states[motor].completed;
}

bool isMotorActive(int motor) {
    if (motor < 1 || motor > 6) return false;
    return motor_states[motor].active;
}

void updateMotorStates() {
    if (!parallel_initialized) return;
    
    uint32_t current_time = micros();
    
    // Обновление состояния каждого активного мотора
    for (int motor = 1; motor <= 6; motor++) {
        if (!motor_states[motor].active || motor_states[motor].completed) {
            continue;
        }
        
        MotorState_t* state = &motor_states[motor];
        
        // Проверка достижения целевого датчика
        if (state->sensor_target) {
            if (readSensor(state->target_sensor_num)) {
                // Датчик сработал - завершаем движение
                state->active = false;
                state->completed = true;
                disableMotorDriver();
                
                Serial.print("M");
                Serial.print(motor);
                Serial.print(" reached sensor D");
                Serial.println(state->target_sensor_num);
                continue;
            }
        }
        
        // Проверка завершения по количеству шагов
        if (state->steps_remaining <= 0) {
            state->active = false;
            state->completed = true;
            disableMotorDriver();
            
            Serial.print("M");
            Serial.print(motor);
            Serial.println(" completed steps");
            continue;
        }
        
        // Вычисление времени для следующего шага
        uint32_t step_interval;
        if (state->step_phase == 0) {
            // Фаза HIGH
            step_interval = state->use_microseconds ? state->pulse_time : (state->pulse_time * 1000);
        } else {
            // Фаза LOW
            step_interval = state->use_microseconds ? state->pause_time : (state->pause_time * 1000);
        }
        
        // Проверка времени для следующего шага
        if (current_time - state->last_step_time >= step_interval) {
            // Выбор мотора и выполнение шага
            selectMotor(motor);
            
            if (state->step_phase == 0) {
                // Переход в HIGH
                digitalWrite(M1_STEP, HIGH);
                state->step_phase = 1;
            } else {
                // Переход в LOW и уменьшение счетчика шагов
                digitalWrite(M1_STEP, LOW);
                state->step_phase = 0;
                state->steps_remaining--;
            }
            
            state->last_step_time = current_time;
        }
    }
    
    last_update_time = current_time;
}

// ===== ПАРАЛЛЕЛЬНЫЕ ОПЕРАЦИИ =====

bool startParallelOperation(const char* operation_name, uint32_t timeout_ms) {
    if (current_operation.active) {
        Serial.println("WARNING: Parallel operation already active, stopping previous");
        stopParallelOperation();
    }
    
    current_operation.active = true;
    current_operation.completed = false;
    current_operation.motors_count = 0;
    current_operation.motors_completed = 0;
    current_operation.start_time = millis();
    current_operation.timeout_ms = timeout_ms;
    strncpy(current_operation.operation_name, operation_name, 31);
    current_operation.operation_name[31] = '\0';
    
    Serial.print("Started parallel operation: ");
    Serial.println(operation_name);
    
    return true;
}

bool isParallelOperationCompleted() {
    if (!current_operation.active) return true;
    
    // Проверка таймаута
    if (millis() - current_operation.start_time > current_operation.timeout_ms) {
        Serial.println("WARNING: Parallel operation timeout!");
        stopParallelOperation();
        return true;
    }
    
    // Подсчет завершенных моторов
    uint8_t completed_count = 0;
    uint8_t active_count = 0;
    
    for (int motor = 1; motor <= 6; motor++) {
        if (motor_states[motor].completed) {
            completed_count++;
        }
        if (motor_states[motor].active) {
            active_count++;
        }
    }
    
    // Операция завершена, если нет активных моторов
    if (active_count == 0) {
        current_operation.completed = true;
        current_operation.active = false;
        
        Serial.print("Parallel operation completed: ");
        Serial.println(current_operation.operation_name);
        return true;
    }
    
    return false;
}

bool waitForParallelOperation(uint32_t timeout_ms) {
    uint32_t start_time = millis();
    uint32_t effective_timeout = (timeout_ms == 0) ? current_operation.timeout_ms : timeout_ms;
    
    while (!isParallelOperationCompleted()) {
        updateMotorStates();
        
        // Проверка таймаута
        if (millis() - start_time > effective_timeout) {
            Serial.println("ERROR: Wait for parallel operation timeout!");
            stopParallelOperation();
            return false;
        }
        
        yield(); // Позволить другим задачам выполняться
        delay(1); // Небольшая задержка для стабильности
    }
    
    return true;
}

void stopParallelOperation() {
    // Остановка всех активных моторов
    for (int motor = 1; motor <= 6; motor++) {
        if (motor_states[motor].active) {
            stopMotorMovement(motor);
        }
    }
    
    disableMotorDriver();
    
    current_operation.active = false;
    current_operation.completed = true;
    
    Serial.print("Stopped parallel operation: ");
    Serial.println(current_operation.operation_name);
}

// ===== ВЫСОКОУРОВНЕВЫЕ ПАРАЛЛЕЛЬНЫЕ ФУНКЦИИ =====

bool parallelTargeting(uint16_t azimuth_steps, uint16_t elevation_steps) {
    Serial.println("=== PARALLEL TARGETING ===");
    Serial.print("Az steps: ");
    Serial.print(azimuth_steps);
    Serial.print(", El steps: ");
    Serial.println(elevation_steps);
    
    startParallelOperation("PARALLEL_TARGETING", 8000);
    
    // Запуск M1 и M2 одновременно
    START_M1_TURBO_CW_ASYNC(azimuth_steps);
    START_M2_TURBO_UP_ASYNC(elevation_steps);
    
    // Ожидание завершения
    bool success = waitForParallelOperation();
    
    Serial.println("PARALLEL TARGETING COMPLETED");
    return success;
}

bool parallelReturnHome() {
    Serial.println("=== PARALLEL RETURN HOME ===");
    
    startParallelOperation("PARALLEL_HOME", 10000);
    
    // Запуск всех моторов одновременно к домашним позициям
    START_M1_TO_D14_ASYNC();    // M1 к датчику D14
    START_M2_TO_D13_ASYNC();    // M2 к датчику D13
    START_M3_TO_D1_ASYNC();     // M3 к датчику D1
    START_M4_TO_D9_ASYNC();     // M4 к датчику D9
    START_M6_TO_D4_ASYNC();     // M6 к датчику D4
    
    // Ожидание завершения
    bool success = waitForParallelOperation();
    
    // Остановка M7
    digitalWrite(M7_LEFT, LOW);
    digitalWrite(M7_RIGHT, LOW);
    digitalWrite(M7_STOP, HIGH);
    
    Serial.println("PARALLEL RETURN HOME COMPLETED");
    return success;
}

bool parallelMotorTest() {
    Serial.println("=== PARALLEL MOTOR TEST ===");
    
    startParallelOperation("PARALLEL_TEST", 5000);
    
    // Запуск всех моторов одновременно для теста
    START_M1_TURBO_CW_ASYNC(STEPS_LARGE);      // 100 шагов
    START_M2_TURBO_UP_ASYNC(STEPS_MEDIUM);     // 50 шагов
    START_M3_TURBO_FWD_ASYNC(STEPS_LARGE);     // 100 шагов
    START_M4_TURBO_FWD_ASYNC(STEPS_FULL);      // 200 шагов
    START_M5_TURBO_FWD_ASYNC(STEPS_LARGE);     // 100 шагов
    START_M6_POWER_FWD_ASYNC(20);              // 20 шагов
    
    // Ожидание завершения
    bool success = waitForParallelOperation();
    
    Serial.println("PARALLEL MOTOR TEST COMPLETED");
    return success;
}

bool parallelShellPreparation() {
    Serial.println("=== PARALLEL SHELL PREPARATION ===");
    
    startParallelOperation("PARALLEL_SHELL_PREP", 6000);
    
    // M6 поиск снаряда и M3 подготовка одновременно
    START_M6_POWER_FWD_ASYNC(30);  // Поворот барабана
    delay(500); // Небольшая задержка
    START_M3_TURBO_FWD_ASYNC(100); // Подача снаряда
    
    // Ожидание завершения
    bool success = waitForParallelOperation();
    
    Serial.println("PARALLEL SHELL PREPARATION COMPLETED");
    return success;
}

bool parallelCalibration() {
    Serial.println("=== PARALLEL CALIBRATION ===");
    
    startParallelOperation("PARALLEL_CALIBRATION", 8000);
    
    // Калибровка всех моторов одновременно
    START_M1_TO_D14_ASYNC();    // M1 к домашней позиции
    START_M2_TO_D13_ASYNC();    // M2 к домашней позиции
    START_M3_TO_D1_ASYNC();     // M3 к домашней позиции
    START_M4_TO_D9_ASYNC();     // M4 к домашней позиции
    START_M6_TO_D4_ASYNC();     // M6 к домашней позиции
    
    // Ожидание завершения
    bool success = waitForParallelOperation();
    
    Serial.println("PARALLEL CALIBRATION COMPLETED");
    return success;
}

// ===== ДИАГНОСТИКА =====

void printParallelStatus() {
    Serial.println("=== PARALLEL CONTROL STATUS ===");
    Serial.print("Initialized: ");
    Serial.println(parallel_initialized ? "YES" : "NO");
    
    Serial.print("Current operation: ");
    Serial.println(current_operation.operation_name);
    Serial.print("Operation active: ");
    Serial.println(current_operation.active ? "YES" : "NO");
    
    if (current_operation.active) {
        Serial.print("Elapsed time: ");
        Serial.print(millis() - current_operation.start_time);
        Serial.println(" ms");
        Serial.print("Progress: ");
        Serial.print(getOperationProgress());
        Serial.println("%");
    }
    
    Serial.println("Motor states:");
    for (int motor = 1; motor <= 6; motor++) {
        Serial.print("M");
        Serial.print(motor);
        Serial.print(": ");
        if (motor_states[motor].active) {
            Serial.print("ACTIVE (");
            Serial.print(motor_states[motor].steps_remaining);
            Serial.print(" steps left)");
        } else if (motor_states[motor].completed) {
            Serial.print("COMPLETED");
        } else {
            Serial.print("IDLE");
        }
        Serial.println();
    }
    Serial.println("===============================");
}

void printMotorStates() {
    for (int motor = 1; motor <= 6; motor++) {
        MotorState_t* state = &motor_states[motor];
        Serial.print("M");
        Serial.print(motor);
        Serial.print(": active=");
        Serial.print(state->active);
        Serial.print(" completed=");
        Serial.print(state->completed);
        Serial.print(" steps=");
        Serial.print(state->steps_remaining);
        Serial.print("/");
        Serial.println(state->steps_total);
    }
}

uint32_t getOperationElapsedTime() {
    if (!current_operation.active) return 0;
    return millis() - current_operation.start_time;
}

float getOperationProgress() {
    if (!current_operation.active) return 100.0;
    
    uint8_t total_motors = 0;
    uint8_t completed_motors = 0;
    
    for (int motor = 1; motor <= 6; motor++) {
        if (motor_states[motor].steps_total > 0) {
            total_motors++;
            if (motor_states[motor].completed) {
                completed_motors++;
            }
        }
    }
    
    if (total_motors == 0) return 100.0;
    return (float)completed_motors / total_motors * 100.0;
}

// ===== БЕЗОПАСНОСТЬ =====

void emergencyStopAllParallel() {
    Serial.println("!!! EMERGENCY STOP ALL PARALLEL !!!");
    
    stopParallelOperation();
    
    // Остановка M7
    digitalWrite(M7_LEFT, LOW);
    digitalWrite(M7_RIGHT, LOW);
    digitalWrite(M7_STOP, HIGH);
    
    // Выключение всех драйверов
    disableMotorDriver();
    digitalWrite(DD16_ENABLE, LOW);
    
    Serial.println("ALL PARALLEL OPERATIONS STOPPED");
}

bool checkParallelSafety() {
    // Проверка таймаутов
    if (current_operation.active) {
        uint32_t elapsed = millis() - current_operation.start_time;
        if (elapsed > current_operation.timeout_ms) {
            Serial.println("WARNING: Parallel operation timeout detected");
            return false;
        }
    }
    
    // Проверка состояния моторов
    for (int motor = 1; motor <= 6; motor++) {
        if (motor_states[motor].active && motor_states[motor].steps_remaining > 10000) {
            Serial.print("WARNING: Motor M");
            Serial.print(motor);
            Serial.println(" has too many steps remaining");
            return false;
        }
    }
    
    return true;
}
