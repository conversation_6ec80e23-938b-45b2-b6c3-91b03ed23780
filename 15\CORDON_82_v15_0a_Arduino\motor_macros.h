/*
 * CORDON-82 v15.0a Arduino Edition
 * УРОВЕНЬ 1: МАКРОСЫ ДВИЖЕНИЙ МОТОРОВ
 * 
 * Базовые макросы для управления моторами в разных скоростных режимах
 */

#ifndef MOTOR_MACROS_H
#define MOTOR_MACROS_H

#include "config.h"

// ===== БАЗОВЫЕ ФУНКЦИИ МОТОРОВ =====
void selectMotor(int motor);
void enableMotorDriver();
void disableMotorDriver();
void motorStepRaw(int motor, int direction, int steps, int pulse_time, int pause_time, bool use_microseconds = false);

// ===== M1 МАКРОСЫ (АЗИМУТ) =====

// M1 ТУРБО режим (1мс+1мс) - максимальная скорость
#define M1_STEP_TURBO(steps, direction) \
    motorStepRaw(MOTOR_M1, direction, steps, M1_TURBO_PULSE, M1_TURBO_PAUSE, false)

// M1 НОРМАЛЬНЫЙ режим (5мс+5мс) - стандартная скорость
#define M1_STEP_NORMAL(steps, direction) \
    motorStepRaw(MOTOR_M1, direction, steps, M1_NORMAL_PULSE, M1_NORMAL_PAUSE, false)

// M1 МЕДЛЕННЫЙ режим (10мс+10мс) - точное позиционирование
#define M1_STEP_SLOW(steps, direction) \
    motorStepRaw(MOTOR_M1, direction, steps, M1_SLOW_PULSE, M1_SLOW_PAUSE, false)

// M1 Направления
#define M1_CW(steps, speed)  M1_STEP_##speed(steps, DIR_CW)
#define M1_CCW(steps, speed) M1_STEP_##speed(steps, DIR_CCW)

// ===== M2 МАКРОСЫ (УГОЛ ВОЗВЫШЕНИЯ) =====

// M2 ТУРБО режим (10мс+2мс) - ускорен в 5 раз
#define M2_STEP_TURBO(steps, direction) \
    motorStepRaw(MOTOR_M2, direction, steps, M2_TURBO_PULSE, M2_TURBO_PAUSE, false)

// M2 НОРМАЛЬНЫЙ режим (25мс+5мс) - стандартная скорость
#define M2_STEP_NORMAL(steps, direction) \
    motorStepRaw(MOTOR_M2, direction, steps, M2_NORMAL_PULSE, M2_NORMAL_PAUSE, false)

// M2 МЕДЛЕННЫЙ режим (50мс+10мс) - точное позиционирование
#define M2_STEP_SLOW(steps, direction) \
    motorStepRaw(MOTOR_M2, direction, steps, M2_SLOW_PULSE, M2_SLOW_PAUSE, false)

// M2 Направления
#define M2_UP(steps, speed)   M2_STEP_##speed(steps, DIR_CW)
#define M2_DOWN(steps, speed) M2_STEP_##speed(steps, DIR_CCW)

// ===== M3 МАКРОСЫ (ПОДАЧА) =====

// M3 ТУРБО режим (1мс+1мс) - ускорен
#define M3_STEP_TURBO(steps, direction) \
    motorStepRaw(MOTOR_M3, direction, steps, M3_TURBO_PULSE, M3_TURBO_PAUSE, false)

// M3 НОРМАЛЬНЫЙ режим (5мс+5мс) - стандартная скорость
#define M3_STEP_NORMAL(steps, direction) \
    motorStepRaw(MOTOR_M3, direction, steps, M3_NORMAL_PULSE, M3_NORMAL_PAUSE, false)

// M3 МЕДЛЕННЫЙ режим (10мс+10мс) - точное позиционирование
#define M3_STEP_SLOW(steps, direction) \
    motorStepRaw(MOTOR_M3, direction, steps, M3_SLOW_PULSE, M3_SLOW_PAUSE, false)

// M3 Направления
#define M3_FORWARD(steps, speed) M3_STEP_##speed(steps, DIR_FORWARD)
#define M3_BACK(steps, speed)    M3_STEP_##speed(steps, DIR_BACK)

// ===== M4 МАКРОСЫ (ДОСЫЛКА) - МИКРОСЕКУНДЫ! =====

// M4 ТУРБО режим (50μS+50μS) - ускорен в 2 раза
#define M4_STEP_TURBO(steps, direction) \
    motorStepRaw(MOTOR_M4, direction, steps, M4_TURBO_PULSE, M4_TURBO_PAUSE, true)

// M4 НОРМАЛЬНЫЙ режим (100μS+100μS) - стандартная скорость
#define M4_STEP_NORMAL(steps, direction) \
    motorStepRaw(MOTOR_M4, direction, steps, M4_NORMAL_PULSE, M4_NORMAL_PAUSE, true)

// M4 МЕДЛЕННЫЙ режим (200μS+200μS) - точное позиционирование
#define M4_STEP_SLOW(steps, direction) \
    motorStepRaw(MOTOR_M4, direction, steps, M4_SLOW_PULSE, M4_SLOW_PAUSE, true)

// M4 Направления
#define M4_FORWARD(steps, speed) M4_STEP_##speed(steps, DIR_FORWARD)
#define M4_BACK(steps, speed)    M4_STEP_##speed(steps, DIR_BACK)

// ===== M5 МАКРОСЫ (ФИКСАЦИЯ) =====

// M5 ТУРБО режим (1мс+1мс) - в 3 раза быстрее
#define M5_STEP_TURBO(steps, direction) \
    motorStepRaw(MOTOR_M5, direction, steps, M5_TURBO_PULSE, M5_TURBO_PAUSE, false)

// M5 НОРМАЛЬНЫЙ режим (3мс+3мс) - стандартная скорость
#define M5_STEP_NORMAL(steps, direction) \
    motorStepRaw(MOTOR_M5, direction, steps, M5_NORMAL_PULSE, M5_NORMAL_PAUSE, false)

// M5 МЕДЛЕННЫЙ режим (5мс+5мс) - точное позиционирование
#define M5_STEP_SLOW(steps, direction) \
    motorStepRaw(MOTOR_M5, direction, steps, M5_SLOW_PULSE, M5_SLOW_PAUSE, false)

// M5 Направления
#define M5_FORWARD(steps, speed) M5_STEP_##speed(steps, DIR_FORWARD)
#define M5_BACK(steps, speed)    M5_STEP_##speed(steps, DIR_BACK)

// ===== M6 МАКРОСЫ (БАРАБАН) =====

// M6 МОЩНЫЙ режим (15мс+1мс) - увеличена мощность для преодоления заедания
#define M6_STEP_POWER(steps, direction) \
    motorStepRaw(MOTOR_M6, direction, steps, M6_POWER_PULSE, M6_POWER_PAUSE, false)

// M6 НОРМАЛЬНЫЙ режим (8мс+2мс) - стандартная скорость
#define M6_STEP_NORMAL(steps, direction) \
    motorStepRaw(MOTOR_M6, direction, steps, M6_NORMAL_PULSE, M6_NORMAL_PAUSE, false)

// M6 МЕДЛЕННЫЙ режим (5мс+5мс) - точное позиционирование
#define M6_STEP_SLOW(steps, direction) \
    motorStepRaw(MOTOR_M6, direction, steps, M6_SLOW_PULSE, M6_SLOW_PAUSE, false)

// M6 Направления
#define M6_FORWARD(steps, speed) M6_STEP_##speed(steps, DIR_FORWARD)
#define M6_BACK(steps, speed)    M6_STEP_##speed(steps, DIR_BACK)

// ===== M7 МАКРОСЫ (DC МОТОР) =====

// M7 управление (DC мотор - без шагов)
#define M7_STOP()     do { digitalWrite(M7_LEFT, LOW); digitalWrite(M7_RIGHT, LOW); digitalWrite(M7_STOP, HIGH); } while(0)
#define M7_LEFT()     do { digitalWrite(M7_LEFT, HIGH); digitalWrite(M7_RIGHT, LOW); digitalWrite(M7_STOP, LOW); } while(0)
#define M7_RIGHT()    do { digitalWrite(M7_LEFT, LOW); digitalWrite(M7_RIGHT, HIGH); digitalWrite(M7_STOP, LOW); } while(0)

// ===== КОМБИНИРОВАННЫЕ МАКРОСЫ =====

// Быстрые движения (ТУРБО режим)
#define FAST_M1_CW(steps)    M1_CW(steps, TURBO)
#define FAST_M1_CCW(steps)   M1_CCW(steps, TURBO)
#define FAST_M2_UP(steps)    M2_UP(steps, TURBO)
#define FAST_M2_DOWN(steps)  M2_DOWN(steps, TURBO)
#define FAST_M3_FWD(steps)   M3_FORWARD(steps, TURBO)
#define FAST_M3_BACK(steps)  M3_BACK(steps, TURBO)
#define FAST_M4_FWD(steps)   M4_FORWARD(steps, TURBO)
#define FAST_M4_BACK(steps)  M4_BACK(steps, TURBO)
#define FAST_M5_FWD(steps)   M5_FORWARD(steps, TURBO)
#define FAST_M5_BACK(steps)  M5_BACK(steps, TURBO)
#define POWER_M6_FWD(steps)  M6_FORWARD(steps, POWER)
#define POWER_M6_BACK(steps) M6_BACK(steps, POWER)

// Точные движения (МЕДЛЕННЫЙ режим)
#define PRECISE_M1_CW(steps)    M1_CW(steps, SLOW)
#define PRECISE_M1_CCW(steps)   M1_CCW(steps, SLOW)
#define PRECISE_M2_UP(steps)    M2_UP(steps, SLOW)
#define PRECISE_M2_DOWN(steps)  M2_DOWN(steps, SLOW)
#define PRECISE_M3_FWD(steps)   M3_FORWARD(steps, SLOW)
#define PRECISE_M3_BACK(steps)  M3_BACK(steps, SLOW)
#define PRECISE_M4_FWD(steps)   M4_FORWARD(steps, SLOW)
#define PRECISE_M4_BACK(steps)  M4_BACK(steps, SLOW)
#define PRECISE_M5_FWD(steps)   M5_FORWARD(steps, SLOW)
#define PRECISE_M5_BACK(steps)  M5_BACK(steps, SLOW)
#define PRECISE_M6_FWD(steps)   M6_FORWARD(steps, SLOW)
#define PRECISE_M6_BACK(steps)  M6_BACK(steps, SLOW)

// ===== СТАНДАРТНЫЕ КОЛИЧЕСТВА ШАГОВ =====

// Малые движения
#define SMALL_MOVE_M1_CW()     FAST_M1_CW(STEPS_SMALL)
#define SMALL_MOVE_M1_CCW()    FAST_M1_CCW(STEPS_SMALL)
#define SMALL_MOVE_M2_UP()     FAST_M2_UP(STEPS_SMALL)
#define SMALL_MOVE_M2_DOWN()   FAST_M2_DOWN(STEPS_SMALL)

// Средние движения
#define MEDIUM_MOVE_M1_CW()    FAST_M1_CW(STEPS_MEDIUM)
#define MEDIUM_MOVE_M1_CCW()   FAST_M1_CCW(STEPS_MEDIUM)
#define MEDIUM_MOVE_M2_UP()    FAST_M2_UP(STEPS_MEDIUM)
#define MEDIUM_MOVE_M2_DOWN()  FAST_M2_DOWN(STEPS_MEDIUM)

// Большие движения
#define LARGE_MOVE_M1_CW()     FAST_M1_CW(STEPS_LARGE)
#define LARGE_MOVE_M1_CCW()    FAST_M1_CCW(STEPS_LARGE)
#define LARGE_MOVE_M2_UP()     FAST_M2_UP(STEPS_LARGE)
#define LARGE_MOVE_M2_DOWN()   FAST_M2_DOWN(STEPS_LARGE)

// Полные движения
#define FULL_MOVE_M1_CW()      FAST_M1_CW(STEPS_FULL)
#define FULL_MOVE_M1_CCW()     FAST_M1_CCW(STEPS_FULL)
#define FULL_MOVE_M2_UP()      FAST_M2_UP(STEPS_FULL)
#define FULL_MOVE_M2_DOWN()    FAST_M2_DOWN(STEPS_FULL)

// ===== ТЕСТОВЫЕ МАКРОСЫ =====

// Тестовые движения для кнопок
#define TEST_M1()  FAST_M1_CW(STEPS_LARGE)    // 100 шагов ТУРБО
#define TEST_M2()  FAST_M2_UP(STEPS_MEDIUM)   // 50 шагов ТУРБО
#define TEST_M3()  FAST_M3_FWD(STEPS_LARGE)   // 100 шагов ТУРБО
#define TEST_M4()  FAST_M4_FWD(STEPS_FULL)    // 200 шагов ТУРБО
#define TEST_M5()  FAST_M5_FWD(STEPS_LARGE)   // 100 шагов ТУРБО
#define TEST_M6()  POWER_M6_FWD(20)           // 20 шагов МОЩНЫЙ

// ===== АВАРИЙНЫЕ МАКРОСЫ =====

// Аварийная остановка всех моторов
#define EMERGENCY_STOP_ALL() do { \
    disableMotorDriver(); \
    M7_STOP(); \
    digitalWrite(DD16_ENABLE, LOW); \
    digitalWrite(ENC1_ENABLE, LOW); \
    digitalWrite(ENC2_ENABLE, LOW); \
} while(0)

// Мягкая остановка всех моторов
#define SOFT_STOP_ALL() do { \
    disableMotorDriver(); \
    M7_STOP(); \
} while(0)

#endif // MOTOR_MACROS_H
